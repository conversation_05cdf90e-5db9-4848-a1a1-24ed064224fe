import superagent = require("superagent");
import { Test } from "../index";
import { AgentOptions, App } from "../types";

declare class TestAgent<Req extends Test = Test> extends superagent.agent<Req> {
    constructor(
        app?: App,
        options?: AgentOptions,
    );

    host(host: string): this;

    "M-SEARCH"(url: string): Req;

    "m-search"(url: string): Req;

    ACL(url: string): Req;

    BIND(url: string): Req;

    CHECKOUT(url: string): Req;

    CONNECT(url: string): Req;

    COPY(url: string): Req;

    DELETE(url: string): Req;

    GET(url: string): Req;

    HEAD(url: string): Req;

    LINK(url: string): Req;

    LOCK(url: string): Req;

    MERGE(url: string): Req;

    MKACTIVITY(url: string): Req;

    MKCALENDAR(url: string): Req;

    MKCOL(url: string): Req;

    MOVE(url: string): Req;

    NOTIFY(url: string): Req;

    OPTIONS(url: string): Req;

    PATCH(url: string): Req;

    POST(url: string): Req;

    PROPFIND(url: string): Req;

    PROPPATCH(url: string): Req;

    PURGE(url: string): Req;

    PUT(url: string): Req;

    REBIND(url: string): Req;

    REPORT(url: string): Req;

    SEARCH(url: string): Req;

    SOURCE(url: string): Req;

    SUBSCRIBE(url: string): Req;

    TRACE(url: string): Req;

    UNBIND(url: string): Req;

    UNLINK(url: string): Req;

    UNLOCK(url: string): Req;

    UNSUBSCRIBE(url: string): Req;

    acl(url: string): Req;

    bind(url: string): Req;

    checkout(url: string): Req;

    connect(url: string): Req;

    copy(url: string): Req;

    del(url: string): Req;

    delete(url: string): Req;

    get(url: string): Req;

    head(url: string): Req;

    link(url: string): Req;

    lock(url: string): Req;

    merge(url: string): Req;

    mkactivity(url: string): Req;

    mkcalendar(url: string): Req;

    mkcol(url: string): Req;

    move(url: string): Req;

    notify(url: string): Req;

    options(url: string): Req;

    patch(url: string): Req;

    post(url: string): Req;

    propfind(url: string): Req;

    proppatch(url: string): Req;

    purge(url: string): Req;

    put(url: string): Req;

    rebind(url: string): Req;

    report(url: string): Req;

    search(url: string): Req;

    source(url: string): Req;

    subscribe(url: string): Req;

    trace(url: string): Req;

    unbind(url: string): Req;

    unlink(url: string): Req;

    unlock(url: string): Req;

    unsubscribe(url: string): Req;
}

export = TestAgent;
