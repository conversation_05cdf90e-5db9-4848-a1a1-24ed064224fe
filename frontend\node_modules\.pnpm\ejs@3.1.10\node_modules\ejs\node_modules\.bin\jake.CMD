@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=C:\workspaces\dd\mbdp\frontend\node_modules\.pnpm\jake@10.9.2\node_modules\jake\bin\node_modules;C:\workspaces\dd\mbdp\frontend\node_modules\.pnpm\jake@10.9.2\node_modules\jake\node_modules;C:\workspaces\dd\mbdp\frontend\node_modules\.pnpm\jake@10.9.2\node_modules;C:\workspaces\dd\mbdp\frontend\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=C:\workspaces\dd\mbdp\frontend\node_modules\.pnpm\jake@10.9.2\node_modules\jake\bin\node_modules;C:\workspaces\dd\mbdp\frontend\node_modules\.pnpm\jake@10.9.2\node_modules\jake\node_modules;C:\workspaces\dd\mbdp\frontend\node_modules\.pnpm\jake@10.9.2\node_modules;C:\workspaces\dd\mbdp\frontend\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\..\..\..\..\jake@10.9.2\node_modules\jake\bin\cli.js" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\..\..\..\..\jake@10.9.2\node_modules\jake\bin\cli.js" %*
)
