#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/proc/cygdrive/c/workspaces/dd/mbdp/frontend/node_modules/.pnpm/d3-dsv@3.0.1/node_modules/d3-dsv/bin/node_modules:/proc/cygdrive/c/workspaces/dd/mbdp/frontend/node_modules/.pnpm/d3-dsv@3.0.1/node_modules/d3-dsv/node_modules:/proc/cygdrive/c/workspaces/dd/mbdp/frontend/node_modules/.pnpm/d3-dsv@3.0.1/node_modules:/proc/cygdrive/c/workspaces/dd/mbdp/frontend/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/proc/cygdrive/c/workspaces/dd/mbdp/frontend/node_modules/.pnpm/d3-dsv@3.0.1/node_modules/d3-dsv/bin/node_modules:/proc/cygdrive/c/workspaces/dd/mbdp/frontend/node_modules/.pnpm/d3-dsv@3.0.1/node_modules/d3-dsv/node_modules:/proc/cygdrive/c/workspaces/dd/mbdp/frontend/node_modules/.pnpm/d3-dsv@3.0.1/node_modules:/proc/cygdrive/c/workspaces/dd/mbdp/frontend/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../d3-dsv/bin/dsv2dsv.js" "$@"
else
  exec node  "$basedir/../d3-dsv/bin/dsv2dsv.js" "$@"
fi
