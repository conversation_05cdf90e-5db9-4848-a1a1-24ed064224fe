{"name": "ts-jest", "version": "29.4.0", "main": "dist/index.js", "types": "dist/index.d.ts", "type": "commonjs", "bin": {"ts-jest": "cli.js"}, "description": "A Jest transformer with source map support that lets you use Jest to test projects written in TypeScript", "scripts": {"prebuild": "rimraf --glob dist coverage *.tgz", "build": "tsc -p tsconfig.build.json", "postbuild": "node scripts/post-build.js", "test": "jest -c=jest.config.ts", "test-e2e-cjs": "jest -c=jest-e2e-cjs.config.ts --no-cache", "test-e2e-esm": "node --experimental-vm-modules --no-warnings node_modules/jest/bin/jest.js -c=jest-e2e-esm.config.ts --no-cache", "test-examples": "node scripts/test-examples.js", "lint": "eslint .", "lint-fix": "eslint --fix .", "lint-prettier": "prettier \"**/*.{yml,yaml,md}\" --write", "lint-prettier-ci": "prettier '**/*.{yml,yaml,md}' --check", "doc": "cd website && npm run start", "doc:build": "cd website && npm run build", "changelog": "conventional-changelog -p angular -i CHANGELOG.md -s -r 1", "prepare": "npm run build", "prepublishOnly": "npm run test", "preversion": "npm run test", "version": "npm run changelog && git add CHANGELOG.md"}, "repository": {"type": "git", "url": "git+https://github.com/kulshekhar/ts-jest.git"}, "keywords": ["jest", "typescript", "sourcemap", "react", "testing"], "author": "<PERSON><PERSON><PERSON><PERSON> <<EMAIL>> (https://github.com/kulshekhar)", "contributors": ["<PERSON><PERSON><PERSON> <<EMAIL>> (https://github.com/huafu)", "<PERSON><PERSON> <<EMAIL>> (https://github.com/ahnpnl)", "<PERSON> <<EMAIL>> (https://github.com/GeeWee)"], "license": "MIT", "bugs": {"url": "https://github.com/kulshekhar/ts-jest/issues"}, "homepage": "https://kulshekhar.github.io/ts-jest", "dependencies": {"bs-logger": "^0.2.6", "ejs": "^3.1.10", "fast-json-stable-stringify": "^2.1.0", "json5": "^2.2.3", "lodash.memoize": "^4.1.2", "make-error": "^1.3.6", "semver": "^7.7.2", "type-fest": "^4.41.0", "yargs-parser": "^21.1.1"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.0 <8", "@jest/transform": "^29.0.0 || ^30.0.0", "@jest/types": "^29.0.0 || ^30.0.0", "babel-jest": "^29.0.0 || ^30.0.0", "jest": "^29.0.0 || ^30.0.0", "jest-util": "^29.0.0 || ^30.0.0", "typescript": ">=4.3 <6"}, "peerDependenciesMeta": {"@babel/core": {"optional": true}, "@jest/transform": {"optional": true}, "@jest/types": {"optional": true}, "babel-jest": {"optional": true}, "esbuild": {"optional": true}, "jest-util": {"optional": true}}, "devDependencies": {"@commitlint/cli": "^19.8.1", "@commitlint/config-angular": "^19.8.1", "@eslint/compat": "^1.2.9", "@eslint/eslintrc": "^3.3.1", "@eslint/js": "^9.28.0", "@jest/globals": "^30.0.0", "@jest/transform": "^30.0.0", "@jest/types": "^30.0.0", "@types/babel__core": "^7.20.5", "@types/ejs": "^3.1.5", "@types/fs-extra": "^11.0.4", "@types/jest": "^29.5.14", "@types/js-yaml": "^4.0.9", "@types/lodash.camelcase": "^4.3.9", "@types/lodash.memoize": "^4.1.9", "@types/lodash.set": "^4.3.9", "@types/micromatch": "^4.0.9", "@types/node": "20.19.0", "@types/semver": "^7.7.0", "@types/yargs": "^17.0.33", "@types/yargs-parser": "21.0.3", "@typescript-eslint/eslint-plugin": "^8.34.0", "@typescript-eslint/parser": "^8.34.0", "babel-jest": "^30.0.0", "conventional-changelog-cli": "^5.0.0", "esbuild": "~0.25.5", "eslint": "^9.28.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-import": "^2.31.0", "eslint-plugin-jest": "^28.13.3", "eslint-plugin-jsdoc": "^50.8.0", "eslint-plugin-prettier": "^4.2.1", "execa": "5.1.1", "fs-extra": "^11.3.0", "glob": "^11.0.2", "glob-gitignore": "^1.0.15", "globals": "^16.2.0", "husky": "^9.1.7", "jest": "^30.0.0", "js-yaml": "^4.1.0", "lint-staged": "^15.5.2", "memfs": "^4.17.2", "prettier": "^2.8.8", "rimraf": "^5.0.10", "ts-node": "^10.9.2", "typescript": "~5.8.3", "typescript-eslint": "^8.34.0"}, "engines": {"node": "^14.15.0 || ^16.10.0 || ^18.0.0 || >=20.0.0"}}