# Installation
> `npm install --save @types/methods`

# Summary
This package contains type definitions for methods (https://github.com/jshttp/methods).

# Details
Files were exported from https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/methods.
## [index.d.ts](https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/methods/index.d.ts)
````ts
type Method =
    | "ACL"
    | "BIND"
    | "CHECKOUT"
    | "CONNECT"
    | "COPY"
    | "DELETE"
    | "GET"
    | "HEAD"
    | "LINK"
    | "LOCK"
    | "M-SEARCH"
    | "MERGE"
    | "MKACTIVITY"
    | "MKCALENDAR"
    | "MKCOL"
    | "MOVE"
    | "NOTIFY"
    | "OPTIONS"
    | "PATCH"
    | "POST"
    | "PROPFIND"
    | "PROPPATCH"
    | "PURGE"
    | "PUT"
    | "REBIND"
    | "REPORT"
    | "SEARCH"
    | "SOURCE"
    | "SUBSCRIBE"
    | "TRACE"
    | "UNBIND"
    | "UNLINK"
    | "UNLOCK"
    | "UNSUBSCRIBE"
    | "acl"
    | "bind"
    | "checkout"
    | "connect"
    | "copy"
    | "delete"
    | "get"
    | "head"
    | "link"
    | "lock"
    | "m-search"
    | "merge"
    | "mkactivity"
    | "mkcalendar"
    | "mkcol"
    | "move"
    | "notify"
    | "options"
    | "patch"
    | "post"
    | "propfind"
    | "proppatch"
    | "purge"
    | "put"
    | "rebind"
    | "report"
    | "search"
    | "source"
    | "subscribe"
    | "trace"
    | "unbind"
    | "unlink"
    | "unlock"
    | "unsubscribe";

declare const methods: Method[];
export = methods;

````

### Additional Details
 * Last updated: Tue, 07 Nov 2023 09:09:39 GMT
 * Dependencies: none

# Credits
These definitions were written by [Carlos Precioso](https://github.com/cprecioso), and [Michel Bitter](https://github.com/michelbitter).
