hoistPattern:
  - '*'
hoistedDependencies:
  '@adobe/css-tools@4.4.3':
    '@adobe/css-tools': private
  '@ampproject/remapping@2.3.0':
    '@ampproject/remapping': private
  '@ant-design/charts-util@0.0.1-alpha.7(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@ant-design/charts-util': private
  '@ant-design/colors@8.0.0':
    '@ant-design/colors': private
  '@ant-design/cssinjs-utils@1.1.3(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@ant-design/cssinjs-utils': private
  '@ant-design/cssinjs@1.24.0(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@ant-design/cssinjs': private
  '@ant-design/fast-color@2.0.6':
    '@ant-design/fast-color': private
  '@ant-design/graphs@2.1.0(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(workerize-loader@2.0.2(webpack@5.100.2))':
    '@ant-design/graphs': private
  '@ant-design/icons-svg@4.4.2':
    '@ant-design/icons-svg': private
  '@ant-design/plots@2.6.0(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@ant-design/plots': private
  '@ant-design/react-slick@1.1.2(react@19.1.0)':
    '@ant-design/react-slick': private
  '@antv/algorithm@0.1.26':
    '@antv/algorithm': private
  '@antv/component@2.1.4':
    '@antv/component': private
  '@antv/coord@0.4.7':
    '@antv/coord': private
  '@antv/event-emitter@0.1.3':
    '@antv/event-emitter': private
  '@antv/expr@1.0.2':
    '@antv/expr': private
  '@antv/g-camera-api@2.0.40':
    '@antv/g-camera-api': private
  '@antv/g-canvas@2.0.47':
    '@antv/g-canvas': private
  '@antv/g-dom-mutation-observer-api@2.0.37':
    '@antv/g-dom-mutation-observer-api': private
  '@antv/g-lite@2.3.1':
    '@antv/g-lite': private
  '@antv/g-math@3.0.1':
    '@antv/g-math': private
  '@antv/g-plugin-canvas-path-generator@2.1.21':
    '@antv/g-plugin-canvas-path-generator': private
  '@antv/g-plugin-canvas-picker@2.1.26':
    '@antv/g-plugin-canvas-picker': private
  '@antv/g-plugin-canvas-renderer@2.3.2':
    '@antv/g-plugin-canvas-renderer': private
  '@antv/g-plugin-dom-interaction@2.1.26':
    '@antv/g-plugin-dom-interaction': private
  '@antv/g-plugin-dragndrop@2.0.37':
    '@antv/g-plugin-dragndrop': private
  '@antv/g-plugin-html-renderer@2.1.26':
    '@antv/g-plugin-html-renderer': private
  '@antv/g-plugin-image-loader@2.1.25':
    '@antv/g-plugin-image-loader': private
  '@antv/g-plugin-svg-picker@2.0.41':
    '@antv/g-plugin-svg-picker': private
  '@antv/g-plugin-svg-renderer@2.2.23':
    '@antv/g-plugin-svg-renderer': private
  '@antv/g-svg@2.0.41':
    '@antv/g-svg': private
  '@antv/g-web-animations-api@2.1.27':
    '@antv/g-web-animations-api': private
  '@antv/g2-extension-plot@0.2.2':
    '@antv/g2-extension-plot': private
  '@antv/g2@5.3.4':
    '@antv/g2': private
  '@antv/g6-extension-react@0.2.4(@antv/g6@5.0.49(workerize-loader@2.0.2(webpack@5.100.2)))(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@antv/g6-extension-react': private
  '@antv/g6@5.0.49(workerize-loader@2.0.2(webpack@5.100.2))':
    '@antv/g6': private
  '@antv/g@6.1.27':
    '@antv/g': private
  '@antv/graphin@3.0.5(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(workerize-loader@2.0.2(webpack@5.100.2))':
    '@antv/graphin': private
  '@antv/graphlib@2.0.4':
    '@antv/graphlib': private
  '@antv/hierarchy@0.6.14':
    '@antv/hierarchy': private
  '@antv/layout@1.2.14-beta.9(workerize-loader@2.0.2(webpack@5.100.2))':
    '@antv/layout': private
  '@antv/scale@0.4.16':
    '@antv/scale': private
  '@antv/util@3.3.11':
    '@antv/util': private
  '@antv/vendor@1.0.11':
    '@antv/vendor': private
  '@asamuzakjp/css-color@3.2.0':
    '@asamuzakjp/css-color': private
  '@babel/code-frame@7.27.1':
    '@babel/code-frame': private
  '@babel/compat-data@7.28.0':
    '@babel/compat-data': private
  '@babel/core@7.28.0':
    '@babel/core': private
  '@babel/generator@7.28.0':
    '@babel/generator': private
  '@babel/helper-compilation-targets@7.27.2':
    '@babel/helper-compilation-targets': private
  '@babel/helper-globals@7.28.0':
    '@babel/helper-globals': private
  '@babel/helper-module-imports@7.27.1':
    '@babel/helper-module-imports': private
  '@babel/helper-module-transforms@7.27.3(@babel/core@7.28.0)':
    '@babel/helper-module-transforms': private
  '@babel/helper-plugin-utils@7.27.1':
    '@babel/helper-plugin-utils': private
  '@babel/helper-string-parser@7.27.1':
    '@babel/helper-string-parser': private
  '@babel/helper-validator-identifier@7.27.1':
    '@babel/helper-validator-identifier': private
  '@babel/helper-validator-option@7.27.1':
    '@babel/helper-validator-option': private
  '@babel/helpers@7.27.6':
    '@babel/helpers': private
  '@babel/parser@7.28.0':
    '@babel/parser': private
  '@babel/plugin-syntax-async-generators@7.8.4(@babel/core@7.28.0)':
    '@babel/plugin-syntax-async-generators': private
  '@babel/plugin-syntax-bigint@7.8.3(@babel/core@7.28.0)':
    '@babel/plugin-syntax-bigint': private
  '@babel/plugin-syntax-class-properties@7.12.13(@babel/core@7.28.0)':
    '@babel/plugin-syntax-class-properties': private
  '@babel/plugin-syntax-class-static-block@7.14.5(@babel/core@7.28.0)':
    '@babel/plugin-syntax-class-static-block': private
  '@babel/plugin-syntax-import-attributes@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-syntax-import-attributes': private
  '@babel/plugin-syntax-import-meta@7.10.4(@babel/core@7.28.0)':
    '@babel/plugin-syntax-import-meta': private
  '@babel/plugin-syntax-json-strings@7.8.3(@babel/core@7.28.0)':
    '@babel/plugin-syntax-json-strings': private
  '@babel/plugin-syntax-jsx@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-syntax-jsx': private
  '@babel/plugin-syntax-logical-assignment-operators@7.10.4(@babel/core@7.28.0)':
    '@babel/plugin-syntax-logical-assignment-operators': private
  '@babel/plugin-syntax-nullish-coalescing-operator@7.8.3(@babel/core@7.28.0)':
    '@babel/plugin-syntax-nullish-coalescing-operator': private
  '@babel/plugin-syntax-numeric-separator@7.10.4(@babel/core@7.28.0)':
    '@babel/plugin-syntax-numeric-separator': private
  '@babel/plugin-syntax-object-rest-spread@7.8.3(@babel/core@7.28.0)':
    '@babel/plugin-syntax-object-rest-spread': private
  '@babel/plugin-syntax-optional-catch-binding@7.8.3(@babel/core@7.28.0)':
    '@babel/plugin-syntax-optional-catch-binding': private
  '@babel/plugin-syntax-optional-chaining@7.8.3(@babel/core@7.28.0)':
    '@babel/plugin-syntax-optional-chaining': private
  '@babel/plugin-syntax-private-property-in-object@7.14.5(@babel/core@7.28.0)':
    '@babel/plugin-syntax-private-property-in-object': private
  '@babel/plugin-syntax-top-level-await@7.14.5(@babel/core@7.28.0)':
    '@babel/plugin-syntax-top-level-await': private
  '@babel/plugin-syntax-typescript@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-syntax-typescript': private
  '@babel/plugin-transform-react-jsx-self@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-react-jsx-self': private
  '@babel/plugin-transform-react-jsx-source@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-react-jsx-source': private
  '@babel/runtime@7.27.6':
    '@babel/runtime': private
  '@babel/template@7.27.2':
    '@babel/template': private
  '@babel/traverse@7.28.0':
    '@babel/traverse': private
  '@babel/types@7.28.1':
    '@babel/types': private
  '@bcoe/v8-coverage@0.2.3':
    '@bcoe/v8-coverage': private
  '@bundled-es-modules/cookie@2.0.1':
    '@bundled-es-modules/cookie': private
  '@bundled-es-modules/statuses@1.0.1':
    '@bundled-es-modules/statuses': private
  '@bundled-es-modules/tough-cookie@0.1.6':
    '@bundled-es-modules/tough-cookie': private
  '@csstools/color-helpers@5.0.2':
    '@csstools/color-helpers': private
  '@csstools/css-calc@2.1.4(@csstools/css-parser-algorithms@3.0.5(@csstools/css-tokenizer@3.0.4))(@csstools/css-tokenizer@3.0.4)':
    '@csstools/css-calc': private
  '@csstools/css-color-parser@3.0.10(@csstools/css-parser-algorithms@3.0.5(@csstools/css-tokenizer@3.0.4))(@csstools/css-tokenizer@3.0.4)':
    '@csstools/css-color-parser': private
  '@csstools/css-parser-algorithms@3.0.5(@csstools/css-tokenizer@3.0.4)':
    '@csstools/css-parser-algorithms': private
  '@csstools/css-tokenizer@3.0.4':
    '@csstools/css-tokenizer': private
  '@cypress/request@3.0.8':
    '@cypress/request': private
  '@cypress/xvfb@1.2.4(supports-color@8.1.1)':
    '@cypress/xvfb': private
  '@discoveryjs/json-ext@0.5.7':
    '@discoveryjs/json-ext': private
  '@emotion/hash@0.8.0':
    '@emotion/hash': private
  '@emotion/is-prop-valid@1.2.2':
    '@emotion/is-prop-valid': private
  '@emotion/memoize@0.8.1':
    '@emotion/memoize': private
  '@emotion/unitless@0.7.5':
    '@emotion/unitless': private
  '@esbuild/aix-ppc64@0.25.6':
    '@esbuild/aix-ppc64': private
  '@esbuild/android-arm64@0.25.6':
    '@esbuild/android-arm64': private
  '@esbuild/android-arm@0.25.6':
    '@esbuild/android-arm': private
  '@esbuild/android-x64@0.25.6':
    '@esbuild/android-x64': private
  '@esbuild/darwin-arm64@0.25.6':
    '@esbuild/darwin-arm64': private
  '@esbuild/darwin-x64@0.25.6':
    '@esbuild/darwin-x64': private
  '@esbuild/freebsd-arm64@0.25.6':
    '@esbuild/freebsd-arm64': private
  '@esbuild/freebsd-x64@0.25.6':
    '@esbuild/freebsd-x64': private
  '@esbuild/linux-arm64@0.25.6':
    '@esbuild/linux-arm64': private
  '@esbuild/linux-arm@0.25.6':
    '@esbuild/linux-arm': private
  '@esbuild/linux-ia32@0.25.6':
    '@esbuild/linux-ia32': private
  '@esbuild/linux-loong64@0.25.6':
    '@esbuild/linux-loong64': private
  '@esbuild/linux-mips64el@0.25.6':
    '@esbuild/linux-mips64el': private
  '@esbuild/linux-ppc64@0.25.6':
    '@esbuild/linux-ppc64': private
  '@esbuild/linux-riscv64@0.25.6':
    '@esbuild/linux-riscv64': private
  '@esbuild/linux-s390x@0.25.6':
    '@esbuild/linux-s390x': private
  '@esbuild/linux-x64@0.25.6':
    '@esbuild/linux-x64': private
  '@esbuild/netbsd-arm64@0.25.6':
    '@esbuild/netbsd-arm64': private
  '@esbuild/netbsd-x64@0.25.6':
    '@esbuild/netbsd-x64': private
  '@esbuild/openbsd-arm64@0.25.6':
    '@esbuild/openbsd-arm64': private
  '@esbuild/openbsd-x64@0.25.6':
    '@esbuild/openbsd-x64': private
  '@esbuild/openharmony-arm64@0.25.6':
    '@esbuild/openharmony-arm64': private
  '@esbuild/sunos-x64@0.25.6':
    '@esbuild/sunos-x64': private
  '@esbuild/win32-arm64@0.25.6':
    '@esbuild/win32-arm64': private
  '@esbuild/win32-ia32@0.25.6':
    '@esbuild/win32-ia32': private
  '@esbuild/win32-x64@0.25.6':
    '@esbuild/win32-x64': private
  '@eslint-community/eslint-utils@4.7.0(eslint@9.31.0)':
    '@eslint-community/eslint-utils': private
  '@eslint-community/regexpp@4.12.1':
    '@eslint-community/regexpp': private
  '@eslint/config-array@0.21.0':
    '@eslint/config-array': private
  '@eslint/config-helpers@0.3.0':
    '@eslint/config-helpers': private
  '@eslint/core@0.15.1':
    '@eslint/core': private
  '@eslint/eslintrc@3.3.1':
    '@eslint/eslintrc': private
  '@eslint/object-schema@2.1.6':
    '@eslint/object-schema': private
  '@eslint/plugin-kit@0.3.3':
    '@eslint/plugin-kit': private
  '@humanfs/core@0.19.1':
    '@humanfs/core': private
  '@humanfs/node@0.16.6':
    '@humanfs/node': private
  '@humanwhocodes/module-importer@1.0.1':
    '@humanwhocodes/module-importer': private
  '@humanwhocodes/retry@0.4.3':
    '@humanwhocodes/retry': private
  '@inquirer/confirm@5.1.13(@types/node@24.0.14)':
    '@inquirer/confirm': private
  '@inquirer/core@10.1.14(@types/node@24.0.14)':
    '@inquirer/core': private
  '@inquirer/figures@1.0.12':
    '@inquirer/figures': private
  '@inquirer/type@3.0.7(@types/node@24.0.14)':
    '@inquirer/type': private
  '@isaacs/cliui@8.0.2':
    '@isaacs/cliui': private
  '@istanbuljs/load-nyc-config@1.1.0':
    '@istanbuljs/load-nyc-config': private
  '@istanbuljs/schema@0.1.3':
    '@istanbuljs/schema': private
  '@jest/console@30.0.4':
    '@jest/console': private
  '@jest/core@30.0.4':
    '@jest/core': private
  '@jest/diff-sequences@30.0.1':
    '@jest/diff-sequences': private
  '@jest/environment-jsdom-abstract@30.0.4(jsdom@26.1.0)':
    '@jest/environment-jsdom-abstract': private
  '@jest/environment@30.0.4':
    '@jest/environment': private
  '@jest/expect-utils@30.0.4':
    '@jest/expect-utils': private
  '@jest/expect@30.0.4':
    '@jest/expect': private
  '@jest/fake-timers@30.0.4':
    '@jest/fake-timers': private
  '@jest/get-type@30.0.1':
    '@jest/get-type': private
  '@jest/globals@30.0.4':
    '@jest/globals': private
  '@jest/pattern@30.0.1':
    '@jest/pattern': private
  '@jest/reporters@30.0.4':
    '@jest/reporters': private
  '@jest/schemas@30.0.1':
    '@jest/schemas': private
  '@jest/snapshot-utils@30.0.4':
    '@jest/snapshot-utils': private
  '@jest/source-map@30.0.1':
    '@jest/source-map': private
  '@jest/test-result@30.0.4':
    '@jest/test-result': private
  '@jest/test-sequencer@30.0.4':
    '@jest/test-sequencer': private
  '@jest/transform@30.0.4':
    '@jest/transform': private
  '@jest/types@30.0.1':
    '@jest/types': private
  '@jridgewell/gen-mapping@0.3.12':
    '@jridgewell/gen-mapping': private
  '@jridgewell/resolve-uri@3.1.2':
    '@jridgewell/resolve-uri': private
  '@jridgewell/source-map@0.3.10':
    '@jridgewell/source-map': private
  '@jridgewell/sourcemap-codec@1.5.4':
    '@jridgewell/sourcemap-codec': private
  '@jridgewell/trace-mapping@0.3.29':
    '@jridgewell/trace-mapping': private
  '@jsonjoy.com/base64@1.1.2(tslib@2.8.1)':
    '@jsonjoy.com/base64': private
  '@jsonjoy.com/json-pack@1.2.0(tslib@2.8.1)':
    '@jsonjoy.com/json-pack': private
  '@jsonjoy.com/util@1.6.0(tslib@2.8.1)':
    '@jsonjoy.com/util': private
  '@leichtgewicht/ip-codec@2.0.5':
    '@leichtgewicht/ip-codec': private
  '@mswjs/interceptors@0.39.3':
    '@mswjs/interceptors': private
  '@naoak/workerize-transferable@0.1.0(workerize-loader@2.0.2(webpack@5.100.2))':
    '@naoak/workerize-transferable': private
  '@noble/hashes@1.8.0':
    '@noble/hashes': private
  '@nodelib/fs.scandir@2.1.5':
    '@nodelib/fs.scandir': private
  '@nodelib/fs.stat@2.0.5':
    '@nodelib/fs.stat': private
  '@nodelib/fs.walk@1.2.8':
    '@nodelib/fs.walk': private
  '@open-draft/deferred-promise@2.2.0':
    '@open-draft/deferred-promise': private
  '@open-draft/logger@0.3.0':
    '@open-draft/logger': private
  '@open-draft/until@2.1.0':
    '@open-draft/until': private
  '@paralleldrive/cuid2@2.2.2':
    '@paralleldrive/cuid2': private
  '@pkgjs/parseargs@0.11.0':
    '@pkgjs/parseargs': private
  '@pkgr/core@0.2.7':
    '@pkgr/core': private
  '@polka/url@1.0.0-next.29':
    '@polka/url': private
  '@rc-component/async-validator@5.0.4':
    '@rc-component/async-validator': private
  '@rc-component/color-picker@2.0.1(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@rc-component/color-picker': private
  '@rc-component/context@1.4.0(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@rc-component/context': private
  '@rc-component/mini-decimal@1.1.0':
    '@rc-component/mini-decimal': private
  '@rc-component/mutate-observer@1.1.0(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@rc-component/mutate-observer': private
  '@rc-component/portal@1.1.2(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@rc-component/portal': private
  '@rc-component/qrcode@1.0.0(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@rc-component/qrcode': private
  '@rc-component/tour@1.15.1(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@rc-component/tour': private
  '@rc-component/trigger@2.2.7(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@rc-component/trigger': private
  '@rc-component/util@1.2.1(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@rc-component/util': private
  '@react-leaflet/core@3.0.0(leaflet@1.9.4)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@react-leaflet/core': private
  '@rolldown/pluginutils@1.0.0-beta.27':
    '@rolldown/pluginutils': private
  '@rollup/rollup-android-arm-eabi@4.45.1':
    '@rollup/rollup-android-arm-eabi': private
  '@rollup/rollup-android-arm64@4.45.1':
    '@rollup/rollup-android-arm64': private
  '@rollup/rollup-darwin-arm64@4.45.1':
    '@rollup/rollup-darwin-arm64': private
  '@rollup/rollup-darwin-x64@4.45.1':
    '@rollup/rollup-darwin-x64': private
  '@rollup/rollup-freebsd-arm64@4.45.1':
    '@rollup/rollup-freebsd-arm64': private
  '@rollup/rollup-freebsd-x64@4.45.1':
    '@rollup/rollup-freebsd-x64': private
  '@rollup/rollup-linux-arm-gnueabihf@4.45.1':
    '@rollup/rollup-linux-arm-gnueabihf': private
  '@rollup/rollup-linux-arm-musleabihf@4.45.1':
    '@rollup/rollup-linux-arm-musleabihf': private
  '@rollup/rollup-linux-arm64-gnu@4.45.1':
    '@rollup/rollup-linux-arm64-gnu': private
  '@rollup/rollup-linux-arm64-musl@4.45.1':
    '@rollup/rollup-linux-arm64-musl': private
  '@rollup/rollup-linux-loongarch64-gnu@4.45.1':
    '@rollup/rollup-linux-loongarch64-gnu': private
  '@rollup/rollup-linux-powerpc64le-gnu@4.45.1':
    '@rollup/rollup-linux-powerpc64le-gnu': private
  '@rollup/rollup-linux-riscv64-gnu@4.45.1':
    '@rollup/rollup-linux-riscv64-gnu': private
  '@rollup/rollup-linux-riscv64-musl@4.45.1':
    '@rollup/rollup-linux-riscv64-musl': private
  '@rollup/rollup-linux-s390x-gnu@4.45.1':
    '@rollup/rollup-linux-s390x-gnu': private
  '@rollup/rollup-linux-x64-gnu@4.45.1':
    '@rollup/rollup-linux-x64-gnu': private
  '@rollup/rollup-linux-x64-musl@4.45.1':
    '@rollup/rollup-linux-x64-musl': private
  '@rollup/rollup-win32-arm64-msvc@4.45.1':
    '@rollup/rollup-win32-arm64-msvc': private
  '@rollup/rollup-win32-ia32-msvc@4.45.1':
    '@rollup/rollup-win32-ia32-msvc': private
  '@rollup/rollup-win32-x64-msvc@4.45.1':
    '@rollup/rollup-win32-x64-msvc': private
  '@sinclair/typebox@0.34.38':
    '@sinclair/typebox': private
  '@sinonjs/commons@3.0.1':
    '@sinonjs/commons': private
  '@sinonjs/fake-timers@13.0.5':
    '@sinonjs/fake-timers': private
  '@standard-schema/spec@1.0.0':
    '@standard-schema/spec': private
  '@standard-schema/utils@0.3.0':
    '@standard-schema/utils': private
  '@testing-library/dom@10.4.0':
    '@testing-library/dom': private
  '@types/aria-query@5.0.4':
    '@types/aria-query': private
  '@types/babel__core@7.20.5':
    '@types/babel__core': private
  '@types/babel__generator@7.27.0':
    '@types/babel__generator': private
  '@types/babel__template@7.4.4':
    '@types/babel__template': private
  '@types/babel__traverse@7.20.7':
    '@types/babel__traverse': private
  '@types/body-parser@1.19.6':
    '@types/body-parser': private
  '@types/bonjour@3.5.13':
    '@types/bonjour': private
  '@types/connect-history-api-fallback@1.5.4':
    '@types/connect-history-api-fallback': private
  '@types/connect@3.4.38':
    '@types/connect': private
  '@types/cookie@0.6.0':
    '@types/cookie': private
  '@types/cookiejar@2.1.5':
    '@types/cookiejar': private
  '@types/d3-array@3.2.1':
    '@types/d3-array': private
  '@types/d3-color@3.1.3':
    '@types/d3-color': private
  '@types/d3-dispatch@3.0.6':
    '@types/d3-dispatch': private
  '@types/d3-dsv@3.0.7':
    '@types/d3-dsv': private
  '@types/d3-ease@3.0.2':
    '@types/d3-ease': private
  '@types/d3-fetch@3.0.7':
    '@types/d3-fetch': private
  '@types/d3-force@3.0.10':
    '@types/d3-force': private
  '@types/d3-format@3.0.4':
    '@types/d3-format': private
  '@types/d3-geo@3.1.0':
    '@types/d3-geo': private
  '@types/d3-hierarchy@3.1.7':
    '@types/d3-hierarchy': private
  '@types/d3-interpolate@3.0.4':
    '@types/d3-interpolate': private
  '@types/d3-path@3.1.1':
    '@types/d3-path': private
  '@types/d3-quadtree@3.0.6':
    '@types/d3-quadtree': private
  '@types/d3-random@3.0.3':
    '@types/d3-random': private
  '@types/d3-scale-chromatic@3.1.0':
    '@types/d3-scale-chromatic': private
  '@types/d3-scale@4.0.9':
    '@types/d3-scale': private
  '@types/d3-shape@3.1.7':
    '@types/d3-shape': private
  '@types/d3-time@3.0.4':
    '@types/d3-time': private
  '@types/d3-timer@3.0.2':
    '@types/d3-timer': private
  '@types/debounce@1.2.4':
    '@types/debounce': private
  '@types/eslint-scope@3.7.7':
    '@types/eslint-scope': private
  '@types/eslint@9.6.1':
    '@types/eslint': private
  '@types/estree@1.0.8':
    '@types/estree': private
  '@types/express-serve-static-core@4.19.6':
    '@types/express-serve-static-core': private
  '@types/express@4.17.23':
    '@types/express': private
  '@types/geojson@7946.0.16':
    '@types/geojson': private
  '@types/history@4.7.11':
    '@types/history': private
  '@types/html-minifier-terser@5.1.2':
    '@types/html-minifier-terser': private
  '@types/http-errors@2.0.5':
    '@types/http-errors': private
  '@types/http-proxy@1.17.16':
    '@types/http-proxy': private
  '@types/istanbul-lib-coverage@2.0.6':
    '@types/istanbul-lib-coverage': private
  '@types/istanbul-lib-report@3.0.3':
    '@types/istanbul-lib-report': private
  '@types/istanbul-reports@3.0.4':
    '@types/istanbul-reports': private
  '@types/jsdom@21.1.7':
    '@types/jsdom': private
  '@types/json-schema@7.0.15':
    '@types/json-schema': private
  '@types/methods@1.1.4':
    '@types/methods': private
  '@types/mime@1.3.5':
    '@types/mime': private
  '@types/node-forge@1.3.13':
    '@types/node-forge': private
  '@types/node@24.0.14':
    '@types/node': private
  '@types/qs@6.14.0':
    '@types/qs': private
  '@types/range-parser@1.2.7':
    '@types/range-parser': private
  '@types/react-router@5.1.20':
    '@types/react-router': private
  '@types/retry@0.12.2':
    '@types/retry': private
  '@types/send@0.17.5':
    '@types/send': private
  '@types/serve-index@1.9.4':
    '@types/serve-index': private
  '@types/serve-static@1.15.8':
    '@types/serve-static': private
  '@types/sinonjs__fake-timers@8.1.1':
    '@types/sinonjs__fake-timers': private
  '@types/sizzle@2.3.9':
    '@types/sizzle': private
  '@types/sockjs@0.3.36':
    '@types/sockjs': private
  '@types/source-list-map@0.1.6':
    '@types/source-list-map': private
  '@types/stack-utils@2.0.3':
    '@types/stack-utils': private
  '@types/statuses@2.0.6':
    '@types/statuses': private
  '@types/stylis@4.2.5':
    '@types/stylis': private
  '@types/superagent@8.1.9':
    '@types/superagent': private
  '@types/tapable@1.0.12':
    '@types/tapable': private
  '@types/tough-cookie@4.0.5':
    '@types/tough-cookie': private
  '@types/uglify-js@3.17.5':
    '@types/uglify-js': private
  '@types/use-sync-external-store@0.0.6':
    '@types/use-sync-external-store': private
  '@types/webpack-sources@3.2.3':
    '@types/webpack-sources': private
  '@types/webpack@4.41.40':
    '@types/webpack': private
  '@types/ws@8.18.1':
    '@types/ws': private
  '@types/yargs-parser@21.0.3':
    '@types/yargs-parser': private
  '@types/yargs@17.0.33':
    '@types/yargs': private
  '@types/yauzl@2.10.3':
    '@types/yauzl': private
  '@typescript-eslint/eslint-plugin@8.37.0(@typescript-eslint/parser@8.37.0(eslint@9.31.0)(typescript@5.8.3))(eslint@9.31.0)(typescript@5.8.3)':
    '@typescript-eslint/eslint-plugin': private
  '@typescript-eslint/parser@8.37.0(eslint@9.31.0)(typescript@5.8.3)':
    '@typescript-eslint/parser': private
  '@typescript-eslint/project-service@8.37.0(typescript@5.8.3)':
    '@typescript-eslint/project-service': private
  '@typescript-eslint/scope-manager@8.37.0':
    '@typescript-eslint/scope-manager': private
  '@typescript-eslint/tsconfig-utils@8.37.0(typescript@5.8.3)':
    '@typescript-eslint/tsconfig-utils': private
  '@typescript-eslint/type-utils@8.37.0(eslint@9.31.0)(typescript@5.8.3)':
    '@typescript-eslint/type-utils': private
  '@typescript-eslint/types@8.37.0':
    '@typescript-eslint/types': private
  '@typescript-eslint/typescript-estree@8.37.0(typescript@5.8.3)':
    '@typescript-eslint/typescript-estree': private
  '@typescript-eslint/utils@8.37.0(eslint@9.31.0)(typescript@5.8.3)':
    '@typescript-eslint/utils': private
  '@typescript-eslint/visitor-keys@8.37.0':
    '@typescript-eslint/visitor-keys': private
  '@ungap/structured-clone@1.3.0':
    '@ungap/structured-clone': private
  '@unrs/resolver-binding-android-arm-eabi@1.11.1':
    '@unrs/resolver-binding-android-arm-eabi': private
  '@unrs/resolver-binding-android-arm64@1.11.1':
    '@unrs/resolver-binding-android-arm64': private
  '@unrs/resolver-binding-darwin-arm64@1.11.1':
    '@unrs/resolver-binding-darwin-arm64': private
  '@unrs/resolver-binding-darwin-x64@1.11.1':
    '@unrs/resolver-binding-darwin-x64': private
  '@unrs/resolver-binding-freebsd-x64@1.11.1':
    '@unrs/resolver-binding-freebsd-x64': private
  '@unrs/resolver-binding-linux-arm-gnueabihf@1.11.1':
    '@unrs/resolver-binding-linux-arm-gnueabihf': private
  '@unrs/resolver-binding-linux-arm-musleabihf@1.11.1':
    '@unrs/resolver-binding-linux-arm-musleabihf': private
  '@unrs/resolver-binding-linux-arm64-gnu@1.11.1':
    '@unrs/resolver-binding-linux-arm64-gnu': private
  '@unrs/resolver-binding-linux-arm64-musl@1.11.1':
    '@unrs/resolver-binding-linux-arm64-musl': private
  '@unrs/resolver-binding-linux-ppc64-gnu@1.11.1':
    '@unrs/resolver-binding-linux-ppc64-gnu': private
  '@unrs/resolver-binding-linux-riscv64-gnu@1.11.1':
    '@unrs/resolver-binding-linux-riscv64-gnu': private
  '@unrs/resolver-binding-linux-riscv64-musl@1.11.1':
    '@unrs/resolver-binding-linux-riscv64-musl': private
  '@unrs/resolver-binding-linux-s390x-gnu@1.11.1':
    '@unrs/resolver-binding-linux-s390x-gnu': private
  '@unrs/resolver-binding-linux-x64-gnu@1.11.1':
    '@unrs/resolver-binding-linux-x64-gnu': private
  '@unrs/resolver-binding-linux-x64-musl@1.11.1':
    '@unrs/resolver-binding-linux-x64-musl': private
  '@unrs/resolver-binding-wasm32-wasi@1.11.1':
    '@unrs/resolver-binding-wasm32-wasi': private
  '@unrs/resolver-binding-win32-arm64-msvc@1.11.1':
    '@unrs/resolver-binding-win32-arm64-msvc': private
  '@unrs/resolver-binding-win32-ia32-msvc@1.11.1':
    '@unrs/resolver-binding-win32-ia32-msvc': private
  '@unrs/resolver-binding-win32-x64-msvc@1.11.1':
    '@unrs/resolver-binding-win32-x64-msvc': private
  '@webassemblyjs/ast@1.14.1':
    '@webassemblyjs/ast': private
  '@webassemblyjs/floating-point-hex-parser@1.13.2':
    '@webassemblyjs/floating-point-hex-parser': private
  '@webassemblyjs/helper-api-error@1.13.2':
    '@webassemblyjs/helper-api-error': private
  '@webassemblyjs/helper-buffer@1.14.1':
    '@webassemblyjs/helper-buffer': private
  '@webassemblyjs/helper-numbers@1.13.2':
    '@webassemblyjs/helper-numbers': private
  '@webassemblyjs/helper-wasm-bytecode@1.13.2':
    '@webassemblyjs/helper-wasm-bytecode': private
  '@webassemblyjs/helper-wasm-section@1.14.1':
    '@webassemblyjs/helper-wasm-section': private
  '@webassemblyjs/ieee754@1.13.2':
    '@webassemblyjs/ieee754': private
  '@webassemblyjs/leb128@1.13.2':
    '@webassemblyjs/leb128': private
  '@webassemblyjs/utf8@1.13.2':
    '@webassemblyjs/utf8': private
  '@webassemblyjs/wasm-edit@1.14.1':
    '@webassemblyjs/wasm-edit': private
  '@webassemblyjs/wasm-gen@1.14.1':
    '@webassemblyjs/wasm-gen': private
  '@webassemblyjs/wasm-opt@1.14.1':
    '@webassemblyjs/wasm-opt': private
  '@webassemblyjs/wasm-parser@1.14.1':
    '@webassemblyjs/wasm-parser': private
  '@webassemblyjs/wast-printer@1.14.1':
    '@webassemblyjs/wast-printer': private
  '@xtuc/ieee754@1.2.0':
    '@xtuc/ieee754': private
  '@xtuc/long@4.2.2':
    '@xtuc/long': private
  accepts@1.3.8:
    accepts: private
  acorn-import-phases@1.0.4(acorn@8.15.0):
    acorn-import-phases: private
  acorn-jsx@5.3.2(acorn@8.15.0):
    acorn-jsx: private
  acorn-walk@8.3.4:
    acorn-walk: private
  acorn@8.15.0:
    acorn: private
  agent-base@7.1.4:
    agent-base: private
  aggregate-error@3.1.0:
    aggregate-error: private
  ajv-formats@2.1.1(ajv@8.17.1):
    ajv-formats: private
  ajv-keywords@5.1.0(ajv@8.17.1):
    ajv-keywords: private
  ajv@6.12.6:
    ajv: private
  ansi-colors@4.1.3:
    ansi-colors: private
  ansi-escapes@4.3.2:
    ansi-escapes: private
  ansi-html-community@0.0.8:
    ansi-html-community: private
  ansi-regex@5.0.1:
    ansi-regex: private
  ansi-styles@4.3.0:
    ansi-styles: private
  anymatch@3.1.3:
    anymatch: private
  arch@2.2.0:
    arch: private
  argparse@2.0.1:
    argparse: private
  aria-query@5.3.2:
    aria-query: private
  array-buffer-byte-length@1.0.2:
    array-buffer-byte-length: private
  array-flatten@1.1.1:
    array-flatten: private
  array.prototype.reduce@1.0.8:
    array.prototype.reduce: private
  arraybuffer.prototype.slice@1.0.4:
    arraybuffer.prototype.slice: private
  asap@2.0.6:
    asap: private
  asn1@0.2.6:
    asn1: private
  assert-plus@1.0.0:
    assert-plus: private
  astral-regex@2.0.0:
    astral-regex: private
  async-function@1.0.0:
    async-function: private
  async@3.2.6:
    async: private
  asynckit@0.4.0:
    asynckit: private
  at-least-node@1.0.0:
    at-least-node: private
  available-typed-arrays@1.0.7:
    available-typed-arrays: private
  aws-sign2@0.7.0:
    aws-sign2: private
  aws4@1.13.2:
    aws4: private
  babel-jest@30.0.4(@babel/core@7.28.0):
    babel-jest: private
  babel-plugin-istanbul@7.0.0:
    babel-plugin-istanbul: private
  babel-plugin-jest-hoist@30.0.1:
    babel-plugin-jest-hoist: private
  babel-preset-current-node-syntax@1.1.0(@babel/core@7.28.0):
    babel-preset-current-node-syntax: private
  babel-preset-jest@30.0.1(@babel/core@7.28.0):
    babel-preset-jest: private
  balanced-match@1.0.2:
    balanced-match: private
  base64-js@1.5.1:
    base64-js: private
  batch@0.6.1:
    batch: private
  bcrypt-pbkdf@1.0.2:
    bcrypt-pbkdf: private
  big.js@5.2.2:
    big.js: private
  binary-extensions@2.3.0:
    binary-extensions: private
  blob-util@2.0.2:
    blob-util: private
  bluebird@3.7.2:
    bluebird: private
  body-parser@1.20.3:
    body-parser: private
  bonjour-service@1.3.0:
    bonjour-service: private
  boolbase@1.0.0:
    boolbase: private
  brace-expansion@1.1.12:
    brace-expansion: private
  braces@3.0.3:
    braces: private
  browserslist@4.25.1:
    browserslist: private
  bs-logger@0.2.6:
    bs-logger: private
  bser@2.1.1:
    bser: private
  bubblesets-js@2.3.4:
    bubblesets-js: private
  buffer-crc32@0.2.13:
    buffer-crc32: private
  buffer-from@1.1.2:
    buffer-from: private
  buffer@5.7.1:
    buffer: private
  bundle-name@4.1.0:
    bundle-name: private
  bytes@3.1.2:
    bytes: private
  cachedir@2.4.0:
    cachedir: private
  call-bind-apply-helpers@1.0.2:
    call-bind-apply-helpers: private
  call-bind@1.0.8:
    call-bind: private
  call-bound@1.0.4:
    call-bound: private
  callsites@3.1.0:
    callsites: private
  camel-case@4.1.2:
    camel-case: private
  camelcase@6.3.0:
    camelcase: private
  camelize@1.0.1:
    camelize: private
  caniuse-lite@1.0.30001727:
    caniuse-lite: private
  caseless@0.12.0:
    caseless: private
  chalk@3.0.0:
    chalk: private
  char-regex@1.0.2:
    char-regex: private
  check-more-types@2.24.0:
    check-more-types: private
  chokidar@3.6.0:
    chokidar: private
  chrome-trace-event@1.0.4:
    chrome-trace-event: private
  ci-info@4.3.0:
    ci-info: private
  cjs-module-lexer@2.1.0:
    cjs-module-lexer: private
  classnames@2.5.1:
    classnames: private
  clean-css@4.2.4:
    clean-css: private
  clean-stack@2.2.0:
    clean-stack: private
  cli-cursor@3.1.0:
    cli-cursor: private
  cli-table3@0.6.1:
    cli-table3: private
  cli-truncate@2.1.0:
    cli-truncate: private
  cli-width@4.1.0:
    cli-width: private
  cliui@8.0.1:
    cliui: private
  clone-deep@4.0.1:
    clone-deep: private
  co@4.6.0:
    co: private
  collect-v8-coverage@1.0.2:
    collect-v8-coverage: private
  color-convert@2.0.1:
    color-convert: private
  color-name@1.1.4:
    color-name: private
  color-string@1.9.1:
    color-string: private
  colorette@2.0.20:
    colorette: private
  colors@1.4.0:
    colors: private
  combined-stream@1.0.8:
    combined-stream: private
  comlink@4.4.2:
    comlink: private
  commander@6.2.1:
    commander: private
  common-tags@1.8.2:
    common-tags: private
  component-emitter@1.3.1:
    component-emitter: private
  compressible@2.0.18:
    compressible: private
  compression@1.8.1:
    compression: private
  compute-scroll-into-view@3.1.1:
    compute-scroll-into-view: private
  concat-map@0.0.1:
    concat-map: private
  connect-history-api-fallback@2.0.0:
    connect-history-api-fallback: private
  content-disposition@0.5.4:
    content-disposition: private
  content-type@1.0.5:
    content-type: private
  convert-source-map@2.0.0:
    convert-source-map: private
  cookie-signature@1.0.6:
    cookie-signature: private
  cookie@0.7.2:
    cookie: private
  cookiejar@2.1.4:
    cookiejar: private
  copy-to-clipboard@3.3.3:
    copy-to-clipboard: private
  core-util-is@1.0.2:
    core-util-is: private
  cross-spawn@7.0.6:
    cross-spawn: private
  css-color-keywords@1.0.0:
    css-color-keywords: private
  css-select@4.3.0:
    css-select: private
  css-to-react-native@3.2.0:
    css-to-react-native: private
  css-what@6.2.2:
    css-what: private
  css.escape@1.5.1:
    css.escape: private
  cssstyle@4.6.0:
    cssstyle: private
  csstype@3.1.3:
    csstype: private
  d3-array@3.2.4:
    d3-array: private
  d3-binarytree@1.0.2:
    d3-binarytree: private
  d3-color@3.1.0:
    d3-color: private
  d3-dispatch@3.0.1:
    d3-dispatch: private
  d3-dsv@3.0.1:
    d3-dsv: private
  d3-ease@3.0.1:
    d3-ease: private
  d3-fetch@3.0.1:
    d3-fetch: private
  d3-force-3d@3.0.6:
    d3-force-3d: private
  d3-force@3.0.0:
    d3-force: private
  d3-format@3.1.0:
    d3-format: private
  d3-geo-projection@4.0.0:
    d3-geo-projection: private
  d3-geo@3.1.1:
    d3-geo: private
  d3-hierarchy@3.1.2:
    d3-hierarchy: private
  d3-interpolate@3.0.1:
    d3-interpolate: private
  d3-octree@1.1.0:
    d3-octree: private
  d3-path@3.1.0:
    d3-path: private
  d3-quadtree@3.0.1:
    d3-quadtree: private
  d3-random@3.0.1:
    d3-random: private
  d3-regression@1.3.10:
    d3-regression: private
  d3-scale-chromatic@3.1.0:
    d3-scale-chromatic: private
  d3-scale@4.0.2:
    d3-scale: private
  d3-shape@3.2.0:
    d3-shape: private
  d3-time-format@4.1.0:
    d3-time-format: private
  d3-time@3.1.0:
    d3-time: private
  d3-timer@3.0.1:
    d3-timer: private
  dagre@0.8.5:
    dagre: private
  dashdash@1.14.1:
    dashdash: private
  data-urls@5.0.0:
    data-urls: private
  data-view-buffer@1.0.2:
    data-view-buffer: private
  data-view-byte-length@1.0.2:
    data-view-byte-length: private
  data-view-byte-offset@1.0.1:
    data-view-byte-offset: private
  dayjs@1.11.13:
    dayjs: private
  debounce@1.2.1:
    debounce: private
  debug@4.4.1(supports-color@8.1.1):
    debug: private
  decimal.js@10.6.0:
    decimal.js: private
  dedent@1.6.0:
    dedent: private
  deep-is@0.1.4:
    deep-is: private
  deepmerge@4.3.1:
    deepmerge: private
  default-browser-id@5.0.0:
    default-browser-id: private
  default-browser@5.2.1:
    default-browser: private
  define-data-property@1.1.4:
    define-data-property: private
  define-lazy-prop@3.0.0:
    define-lazy-prop: private
  define-properties@1.2.1:
    define-properties: private
  delayed-stream@1.0.0:
    delayed-stream: private
  depd@2.0.0:
    depd: private
  dequal@2.0.3:
    dequal: private
  destroy@1.2.0:
    destroy: private
  detect-newline@3.1.0:
    detect-newline: private
  detect-node@2.1.0:
    detect-node: private
  dezalgo@1.0.4:
    dezalgo: private
  dns-packet@5.6.1:
    dns-packet: private
  dom-accessibility-api@0.6.3:
    dom-accessibility-api: private
  dom-converter@0.2.0:
    dom-converter: private
  dom-serializer@1.4.1:
    dom-serializer: private
  domelementtype@2.3.0:
    domelementtype: private
  domhandler@4.3.1:
    domhandler: private
  domutils@2.8.0:
    domutils: private
  dot-case@3.0.4:
    dot-case: private
  dunder-proto@1.0.1:
    dunder-proto: private
  duplexer@0.1.2:
    duplexer: private
  eastasianwidth@0.2.0:
    eastasianwidth: private
  ecc-jsbn@0.1.2:
    ecc-jsbn: private
  ee-first@1.1.1:
    ee-first: private
  ejs@3.1.10:
    ejs: private
  electron-to-chromium@1.5.187:
    electron-to-chromium: private
  emittery@0.13.1:
    emittery: private
  emoji-regex@8.0.0:
    emoji-regex: private
  emojis-list@3.0.0:
    emojis-list: private
  encodeurl@2.0.0:
    encodeurl: private
  end-of-stream@1.4.5:
    end-of-stream: private
  enhanced-resolve@5.18.2:
    enhanced-resolve: private
  enquirer@2.4.1:
    enquirer: private
  entities@6.0.1:
    entities: private
  error-ex@1.3.2:
    error-ex: private
  es-abstract@1.24.0:
    es-abstract: private
  es-array-method-boxes-properly@1.0.0:
    es-array-method-boxes-properly: private
  es-define-property@1.0.1:
    es-define-property: private
  es-errors@1.3.0:
    es-errors: private
  es-module-lexer@1.7.0:
    es-module-lexer: private
  es-object-atoms@1.1.1:
    es-object-atoms: private
  es-set-tostringtag@2.1.0:
    es-set-tostringtag: private
  es-to-primitive@1.3.0:
    es-to-primitive: private
  esbuild@0.25.6:
    esbuild: private
  escalade@3.2.0:
    escalade: private
  escape-html@1.0.3:
    escape-html: private
  escape-string-regexp@4.0.0:
    escape-string-regexp: private
  eslint-scope@8.4.0:
    eslint-scope: private
  eslint-visitor-keys@4.2.1:
    eslint-visitor-keys: private
  espree@10.4.0:
    espree: private
  esprima@4.0.1:
    esprima: private
  esquery@1.6.0:
    esquery: private
  esrecurse@4.3.0:
    esrecurse: private
  estraverse@5.3.0:
    estraverse: private
  esutils@2.0.3:
    esutils: private
  etag@1.8.1:
    etag: private
  eventemitter2@6.4.7:
    eventemitter2: private
  eventemitter3@4.0.7:
    eventemitter3: private
  events@3.3.0:
    events: private
  execa@4.1.0:
    execa: private
  executable@4.1.1:
    executable: private
  exit-x@0.2.2:
    exit-x: private
  expect@30.0.4:
    expect: private
  express@4.21.2:
    express: private
  extend@3.0.2:
    extend: private
  extract-zip@2.0.1(supports-color@8.1.1):
    extract-zip: private
  extsprintf@1.3.0:
    extsprintf: private
  fast-deep-equal@3.1.3:
    fast-deep-equal: private
  fast-glob@3.3.3:
    fast-glob: private
  fast-json-stable-stringify@2.1.0:
    fast-json-stable-stringify: private
  fast-levenshtein@2.0.6:
    fast-levenshtein: private
  fast-safe-stringify@2.1.1:
    fast-safe-stringify: private
  fast-uri@3.0.6:
    fast-uri: private
  fastq@1.19.1:
    fastq: private
  faye-websocket@0.11.4:
    faye-websocket: private
  fb-watchman@2.0.2:
    fb-watchman: private
  fd-slicer@1.1.0:
    fd-slicer: private
  fdir@6.4.6(picomatch@4.0.3):
    fdir: private
  fecha@4.2.3:
    fecha: private
  figures@3.2.0:
    figures: private
  file-entry-cache@8.0.0:
    file-entry-cache: private
  filelist@1.0.4:
    filelist: private
  fill-range@7.1.1:
    fill-range: private
  finalhandler@1.3.1:
    finalhandler: private
  find-up@6.3.0:
    find-up: private
  flat-cache@4.0.1:
    flat-cache: private
  flat@5.0.2:
    flat: private
  flatted@3.3.3:
    flatted: private
  flru@1.0.2:
    flru: private
  follow-redirects@1.15.9:
    follow-redirects: private
  for-each@0.3.5:
    for-each: private
  foreground-child@3.3.1:
    foreground-child: private
  forever-agent@0.6.1:
    forever-agent: private
  form-data@4.0.4:
    form-data: private
  formidable@3.5.4:
    formidable: private
  forwarded@0.2.0:
    forwarded: private
  fresh@0.5.2:
    fresh: private
  fs-extra@9.1.0:
    fs-extra: private
  fs.realpath@1.0.0:
    fs.realpath: private
  fsevents@2.3.3:
    fsevents: private
  function-bind@1.1.2:
    function-bind: private
  function.prototype.name@1.1.8:
    function.prototype.name: private
  functions-have-names@1.2.3:
    functions-have-names: private
  gensync@1.0.0-beta.2:
    gensync: private
  get-caller-file@2.0.5:
    get-caller-file: private
  get-intrinsic@1.3.0:
    get-intrinsic: private
  get-package-type@0.1.0:
    get-package-type: private
  get-proto@1.0.1:
    get-proto: private
  get-stream@5.2.0:
    get-stream: private
  get-symbol-description@1.1.0:
    get-symbol-description: private
  getos@3.2.1:
    getos: private
  getpass@0.1.7:
    getpass: private
  gl-matrix@3.4.3:
    gl-matrix: private
  glob-parent@6.0.2:
    glob-parent: private
  glob-to-regexp@0.4.1:
    glob-to-regexp: private
  glob@10.4.5:
    glob: private
  global-dirs@3.0.1:
    global-dirs: private
  globalthis@1.0.4:
    globalthis: private
  gopd@1.2.0:
    gopd: private
  graceful-fs@4.2.11:
    graceful-fs: private
  graphemer@1.4.0:
    graphemer: private
  graphlib@2.1.8:
    graphlib: private
  graphql@16.11.0:
    graphql: private
  gzip-size@6.0.0:
    gzip-size: private
  handle-thing@2.0.1:
    handle-thing: private
  has-bigints@1.1.0:
    has-bigints: private
  has-flag@4.0.0:
    has-flag: private
  has-property-descriptors@1.0.2:
    has-property-descriptors: private
  has-proto@1.2.0:
    has-proto: private
  has-symbols@1.1.0:
    has-symbols: private
  has-tostringtag@1.0.2:
    has-tostringtag: private
  hasha@5.2.2:
    hasha: private
  hasown@2.0.2:
    hasown: private
  he@1.2.0:
    he: private
  headers-polyfill@4.0.3:
    headers-polyfill: private
  hpack.js@2.1.6:
    hpack.js: private
  html-encoding-sniffer@4.0.0:
    html-encoding-sniffer: private
  html-escaper@2.0.2:
    html-escaper: private
  html-minifier-terser@5.1.1:
    html-minifier-terser: private
  html-webpack-plugin@4.5.2(webpack@5.100.2):
    html-webpack-plugin-4: private
  html-webpack-plugin@5.6.3(webpack@5.100.2):
    html-webpack-plugin-5: private
  htmlparser2@6.1.0:
    htmlparser2: private
  http-deceiver@1.2.7:
    http-deceiver: private
  http-errors@2.0.0:
    http-errors: private
  http-parser-js@0.5.10:
    http-parser-js: private
  http-proxy-agent@7.0.2:
    http-proxy-agent: private
  http-proxy-middleware@2.0.9(@types/express@4.17.23):
    http-proxy-middleware: private
  http-proxy@1.18.1:
    http-proxy: private
  http-signature@1.4.0:
    http-signature: private
  https-proxy-agent@7.0.6:
    https-proxy-agent: private
  human-signals@1.1.1:
    human-signals: private
  hyperdyperid@1.2.0:
    hyperdyperid: private
  iconv-lite@0.6.3:
    iconv-lite: private
  ieee754@1.2.1:
    ieee754: private
  ignore@5.3.2:
    ignore: private
  immer@10.1.1:
    immer: private
  import-fresh@3.3.1:
    import-fresh: private
  import-local@3.2.0:
    import-local: private
  imurmurhash@0.1.4:
    imurmurhash: private
  indent-string@4.0.0:
    indent-string: private
  inflight@1.0.6:
    inflight: private
  inherits@2.0.3:
    inherits: private
  ini@2.0.0:
    ini: private
  internal-slot@1.1.0:
    internal-slot: private
  internmap@2.0.3:
    internmap: private
  ipaddr.js@2.2.0:
    ipaddr.js: private
  is-any-array@2.0.1:
    is-any-array: private
  is-array-buffer@3.0.5:
    is-array-buffer: private
  is-arrayish@0.2.1:
    is-arrayish: private
  is-async-function@2.1.1:
    is-async-function: private
  is-bigint@1.1.0:
    is-bigint: private
  is-binary-path@2.1.0:
    is-binary-path: private
  is-boolean-object@1.2.2:
    is-boolean-object: private
  is-callable@1.2.7:
    is-callable: private
  is-data-view@1.0.2:
    is-data-view: private
  is-date-object@1.1.0:
    is-date-object: private
  is-docker@3.0.0:
    is-docker: private
  is-extglob@2.1.1:
    is-extglob: private
  is-finalizationregistry@1.1.1:
    is-finalizationregistry: private
  is-fullwidth-code-point@3.0.0:
    is-fullwidth-code-point: private
  is-generator-fn@2.1.0:
    is-generator-fn: private
  is-generator-function@1.1.0:
    is-generator-function: private
  is-glob@4.0.3:
    is-glob: private
  is-inside-container@1.0.0:
    is-inside-container: private
  is-installed-globally@0.4.0:
    is-installed-globally: private
  is-map@2.0.3:
    is-map: private
  is-negative-zero@2.0.3:
    is-negative-zero: private
  is-network-error@1.1.0:
    is-network-error: private
  is-node-process@1.2.0:
    is-node-process: private
  is-number-object@1.1.1:
    is-number-object: private
  is-number@7.0.0:
    is-number: private
  is-path-inside@3.0.3:
    is-path-inside: private
  is-plain-obj@3.0.0:
    is-plain-obj: private
  is-plain-object@2.0.4:
    is-plain-object: private
  is-potential-custom-element-name@1.0.1:
    is-potential-custom-element-name: private
  is-regex@1.2.1:
    is-regex: private
  is-set@2.0.3:
    is-set: private
  is-shared-array-buffer@1.0.4:
    is-shared-array-buffer: private
  is-stream@2.0.1:
    is-stream: private
  is-string@1.1.1:
    is-string: private
  is-symbol@1.1.1:
    is-symbol: private
  is-typed-array@1.1.15:
    is-typed-array: private
  is-typedarray@1.0.0:
    is-typedarray: private
  is-unicode-supported@0.1.0:
    is-unicode-supported: private
  is-weakmap@2.0.2:
    is-weakmap: private
  is-weakref@1.1.1:
    is-weakref: private
  is-weakset@2.0.4:
    is-weakset: private
  is-wsl@3.1.0:
    is-wsl: private
  isarray@2.0.5:
    isarray: private
  isexe@2.0.0:
    isexe: private
  isobject@3.0.1:
    isobject: private
  isstream@0.1.2:
    isstream: private
  istanbul-lib-coverage@3.2.2:
    istanbul-lib-coverage: private
  istanbul-lib-instrument@6.0.3:
    istanbul-lib-instrument: private
  istanbul-lib-report@3.0.1:
    istanbul-lib-report: private
  istanbul-lib-source-maps@5.0.6:
    istanbul-lib-source-maps: private
  istanbul-reports@3.1.7:
    istanbul-reports: private
  jackspeak@3.4.3:
    jackspeak: private
  jake@10.9.2:
    jake: private
  jest-changed-files@30.0.2:
    jest-changed-files: private
  jest-circus@30.0.4:
    jest-circus: private
  jest-cli@30.0.4(@types/node@24.0.14):
    jest-cli: private
  jest-config@30.0.4(@types/node@24.0.14):
    jest-config: private
  jest-diff@30.0.4:
    jest-diff: private
  jest-docblock@30.0.1:
    jest-docblock: private
  jest-each@30.0.2:
    jest-each: private
  jest-environment-node@30.0.4:
    jest-environment-node: private
  jest-haste-map@30.0.2:
    jest-haste-map: private
  jest-leak-detector@30.0.2:
    jest-leak-detector: private
  jest-matcher-utils@30.0.4:
    jest-matcher-utils: private
  jest-message-util@30.0.2:
    jest-message-util: private
  jest-mock@30.0.2:
    jest-mock: private
  jest-pnp-resolver@1.2.3(jest-resolve@30.0.2):
    jest-pnp-resolver: private
  jest-regex-util@30.0.1:
    jest-regex-util: private
  jest-resolve-dependencies@30.0.4:
    jest-resolve-dependencies: private
  jest-resolve@30.0.2:
    jest-resolve: private
  jest-runner@30.0.4:
    jest-runner: private
  jest-runtime@30.0.4:
    jest-runtime: private
  jest-snapshot@30.0.4:
    jest-snapshot: private
  jest-util@30.0.2:
    jest-util: private
  jest-validate@30.0.2:
    jest-validate: private
  jest-watcher@30.0.4:
    jest-watcher: private
  jest-worker@30.0.2:
    jest-worker: private
  js-tokens@4.0.0:
    js-tokens: private
  js-yaml@4.1.0:
    js-yaml: private
  jsbn@0.1.1:
    jsbn: private
  jsdom@26.1.0:
    jsdom: private
  jsesc@3.1.0:
    jsesc: private
  json-buffer@3.0.1:
    json-buffer: private
  json-parse-even-better-errors@2.3.1:
    json-parse-even-better-errors: private
  json-schema-traverse@0.4.1:
    json-schema-traverse: private
  json-schema@0.4.0:
    json-schema: private
  json-stable-stringify-without-jsonify@1.0.1:
    json-stable-stringify-without-jsonify: private
  json-stringify-safe@5.0.1:
    json-stringify-safe: private
  json2mq@0.2.0:
    json2mq: private
  json5@2.2.3:
    json5: private
  jsonfile@6.1.0:
    jsonfile: private
  jsprim@2.0.2:
    jsprim: private
  keyv@4.5.4:
    keyv: private
  kind-of@6.0.3:
    kind-of: private
  launch-editor@2.10.0:
    launch-editor: private
  lazy-ass@1.6.0:
    lazy-ass: private
  leven@3.1.0:
    leven: private
  levn@0.4.1:
    levn: private
  lines-and-columns@1.2.4:
    lines-and-columns: private
  listr2@3.14.0(enquirer@2.4.1):
    listr2: private
  loader-runner@4.3.0:
    loader-runner: private
  loader-utils@1.4.2:
    loader-utils: private
  local-pkg@0.4.1:
    local-pkg: private
  locate-path@6.0.0:
    locate-path: private
  lodash.memoize@4.1.2:
    lodash.memoize: private
  lodash.merge@4.6.2:
    lodash.merge: private
  lodash.once@4.1.1:
    lodash.once: private
  lodash@4.17.21:
    lodash: private
  log-symbols@4.1.0:
    log-symbols: private
  log-update@4.0.0:
    log-update: private
  lower-case@2.0.2:
    lower-case: private
  lru-cache@5.1.1:
    lru-cache: private
  lz-string@1.5.0:
    lz-string: private
  make-dir@4.0.0:
    make-dir: private
  make-error@1.3.6:
    make-error: private
  makeerror@1.0.12:
    makeerror: private
  math-intrinsics@1.1.0:
    math-intrinsics: private
  media-typer@0.3.0:
    media-typer: private
  memfs@4.17.2:
    memfs: private
  merge-descriptors@1.0.3:
    merge-descriptors: private
  merge-stream@2.0.0:
    merge-stream: private
  merge2@1.4.1:
    merge2: private
  methods@1.1.2:
    methods: private
  micromatch@4.0.8:
    micromatch: private
  mime-db@1.52.0:
    mime-db: private
  mime-types@2.1.35:
    mime-types: private
  mime@2.6.0:
    mime: private
  mimic-fn@2.1.0:
    mimic-fn: private
  min-indent@1.0.1:
    min-indent: private
  minimalistic-assert@1.0.1:
    minimalistic-assert: private
  minimatch@3.1.2:
    minimatch: private
  minimist@1.2.8:
    minimist: private
  minipass@7.1.2:
    minipass: private
  ml-array-max@1.2.4:
    ml-array-max: private
  ml-array-min@1.2.3:
    ml-array-min: private
  ml-array-rescale@1.3.7:
    ml-array-rescale: private
  ml-matrix@6.12.1:
    ml-matrix: private
  mrmime@2.0.1:
    mrmime: private
  ms@2.1.3:
    ms: private
  multicast-dns@7.2.5:
    multicast-dns: private
  mute-stream@2.0.0:
    mute-stream: private
  nanoid@3.3.11:
    nanoid: private
  napi-postinstall@0.3.0:
    napi-postinstall: private
  natural-compare@1.4.0:
    natural-compare: private
  negotiator@0.6.4:
    negotiator: private
  neo-async@2.6.2:
    neo-async: private
  no-case@3.0.4:
    no-case: private
  node-forge@1.3.1:
    node-forge: private
  node-int64@0.4.0:
    node-int64: private
  node-releases@2.0.19:
    node-releases: private
  normalize-path@3.0.0:
    normalize-path: private
  npm-run-path@4.0.1:
    npm-run-path: private
  nth-check@2.1.1:
    nth-check: private
  nwsapi@2.2.20:
    nwsapi: private
  object-inspect@1.13.4:
    object-inspect: private
  object-keys@1.1.1:
    object-keys: private
  object.assign@4.1.7:
    object.assign: private
  object.getownpropertydescriptors@2.1.8:
    object.getownpropertydescriptors: private
  obuf@1.1.2:
    obuf: private
  on-finished@2.4.1:
    on-finished: private
  on-headers@1.1.0:
    on-headers: private
  once@1.4.0:
    once: private
  onetime@5.1.2:
    onetime: private
  open@10.2.0:
    open: private
  opener@1.5.2:
    opener: private
  optionator@0.9.4:
    optionator: private
  ospath@1.2.2:
    ospath: private
  outvariant@1.4.3:
    outvariant: private
  own-keys@1.0.1:
    own-keys: private
  p-limit@3.1.0:
    p-limit: private
  p-locate@5.0.0:
    p-locate: private
  p-map@4.0.0:
    p-map: private
  p-retry@6.2.1:
    p-retry: private
  p-try@2.2.0:
    p-try: private
  package-json-from-dist@1.0.1:
    package-json-from-dist: private
  param-case@3.0.4:
    param-case: private
  parent-module@1.0.1:
    parent-module: private
  parse-json@5.2.0:
    parse-json: private
  parse5@7.3.0:
    parse5: private
  parseurl@1.3.3:
    parseurl: private
  pascal-case@3.1.2:
    pascal-case: private
  path-exists@4.0.0:
    path-exists: private
  path-is-absolute@1.0.1:
    path-is-absolute: private
  path-key@3.1.1:
    path-key: private
  path-scurry@1.11.1:
    path-scurry: private
  path-to-regexp@6.3.0:
    path-to-regexp: private
  pdfast@0.2.0:
    pdfast: private
  pend@1.2.0:
    pend: private
  performance-now@2.1.0:
    performance-now: private
  picocolors@1.1.1:
    picocolors: private
  picomatch@4.0.3:
    picomatch: private
  pify@2.3.0:
    pify: private
  pirates@4.0.7:
    pirates: private
  pkg-dir@4.2.0:
    pkg-dir: private
  possible-typed-array-names@1.1.0:
    possible-typed-array-names: private
  postcss-value-parser@4.2.0:
    postcss-value-parser: private
  postcss@8.5.6:
    postcss: private
  prelude-ls@1.2.1:
    prelude-ls: private
  pretty-bytes@5.6.0:
    pretty-bytes: private
  pretty-error@2.1.2:
    pretty-error: private
  pretty-format@30.0.2:
    pretty-format: private
  process-nextick-args@2.0.1:
    process-nextick-args: private
  process@0.11.10:
    process: private
  proxy-addr@2.0.7:
    proxy-addr: private
  proxy-from-env@1.1.0:
    proxy-from-env: private
  psl@1.15.0:
    psl: private
  pump@3.0.3:
    pump: private
  punycode@2.3.1:
    punycode: private
  pure-rand@7.0.1:
    pure-rand: private
  qs@6.14.0:
    qs: private
  querystringify@2.2.0:
    querystringify: private
  queue-microtask@1.2.3:
    queue-microtask: private
  quickselect@2.0.0:
    quickselect: private
  randombytes@2.1.0:
    randombytes: private
  range-parser@1.2.1:
    range-parser: private
  raw-body@2.5.2:
    raw-body: private
  rbush@3.0.1:
    rbush: private
  rc-cascader@3.34.0(react-dom@19.1.0(react@19.1.0))(react@19.1.0):
    rc-cascader: private
  rc-checkbox@3.5.0(react-dom@19.1.0(react@19.1.0))(react@19.1.0):
    rc-checkbox: private
  rc-collapse@3.9.0(react-dom@19.1.0(react@19.1.0))(react@19.1.0):
    rc-collapse: private
  rc-dialog@9.6.0(react-dom@19.1.0(react@19.1.0))(react@19.1.0):
    rc-dialog: private
  rc-drawer@7.3.0(react-dom@19.1.0(react@19.1.0))(react@19.1.0):
    rc-drawer: private
  rc-dropdown@4.2.1(react-dom@19.1.0(react@19.1.0))(react@19.1.0):
    rc-dropdown: private
  rc-field-form@2.7.0(react-dom@19.1.0(react@19.1.0))(react@19.1.0):
    rc-field-form: private
  rc-image@7.12.0(react-dom@19.1.0(react@19.1.0))(react@19.1.0):
    rc-image: private
  rc-input-number@9.5.0(react-dom@19.1.0(react@19.1.0))(react@19.1.0):
    rc-input-number: private
  rc-input@1.8.0(react-dom@19.1.0(react@19.1.0))(react@19.1.0):
    rc-input: private
  rc-mentions@2.20.0(react-dom@19.1.0(react@19.1.0))(react@19.1.0):
    rc-mentions: private
  rc-menu@9.16.1(react-dom@19.1.0(react@19.1.0))(react@19.1.0):
    rc-menu: private
  rc-motion@2.9.5(react-dom@19.1.0(react@19.1.0))(react@19.1.0):
    rc-motion: private
  rc-notification@5.6.4(react-dom@19.1.0(react@19.1.0))(react@19.1.0):
    rc-notification: private
  rc-overflow@1.4.1(react-dom@19.1.0(react@19.1.0))(react@19.1.0):
    rc-overflow: private
  rc-pagination@5.1.0(react-dom@19.1.0(react@19.1.0))(react@19.1.0):
    rc-pagination: private
  rc-picker@4.11.3(dayjs@1.11.13)(react-dom@19.1.0(react@19.1.0))(react@19.1.0):
    rc-picker: private
  rc-progress@4.0.0(react-dom@19.1.0(react@19.1.0))(react@19.1.0):
    rc-progress: private
  rc-rate@2.13.1(react-dom@19.1.0(react@19.1.0))(react@19.1.0):
    rc-rate: private
  rc-resize-observer@1.4.3(react-dom@19.1.0(react@19.1.0))(react@19.1.0):
    rc-resize-observer: private
  rc-segmented@2.7.0(react-dom@19.1.0(react@19.1.0))(react@19.1.0):
    rc-segmented: private
  rc-select@14.16.8(react-dom@19.1.0(react@19.1.0))(react@19.1.0):
    rc-select: private
  rc-slider@11.1.8(react-dom@19.1.0(react@19.1.0))(react@19.1.0):
    rc-slider: private
  rc-steps@6.0.1(react-dom@19.1.0(react@19.1.0))(react@19.1.0):
    rc-steps: private
  rc-switch@4.1.0(react-dom@19.1.0(react@19.1.0))(react@19.1.0):
    rc-switch: private
  rc-table@7.51.1(react-dom@19.1.0(react@19.1.0))(react@19.1.0):
    rc-table: private
  rc-tabs@15.6.1(react-dom@19.1.0(react@19.1.0))(react@19.1.0):
    rc-tabs: private
  rc-textarea@1.10.0(react-dom@19.1.0(react@19.1.0))(react@19.1.0):
    rc-textarea: private
  rc-tooltip@6.4.0(react-dom@19.1.0(react@19.1.0))(react@19.1.0):
    rc-tooltip: private
  rc-tree-select@5.27.0(react-dom@19.1.0(react@19.1.0))(react@19.1.0):
    rc-tree-select: private
  rc-tree@5.13.1(react-dom@19.1.0(react@19.1.0))(react@19.1.0):
    rc-tree: private
  rc-upload@4.9.2(react-dom@19.1.0(react@19.1.0))(react@19.1.0):
    rc-upload: private
  rc-util@5.44.4(react-dom@19.1.0(react@19.1.0))(react@19.1.0):
    rc-util: private
  rc-virtual-list@3.19.1(react-dom@19.1.0(react@19.1.0))(react@19.1.0):
    rc-virtual-list: private
  react-is@18.3.1:
    react-is: private
  react-refresh@0.17.0:
    react-refresh: private
  react-router@7.7.0(react-dom@19.1.0(react@19.1.0))(react@19.1.0):
    react-router: private
  readable-stream@3.6.2:
    readable-stream: private
  readdirp@3.6.0:
    readdirp: private
  redent@3.0.0:
    redent: private
  redux-thunk@3.1.0(redux@5.0.1):
    redux-thunk: private
  redux@5.0.1:
    redux: private
  reflect.getprototypeof@1.0.10:
    reflect.getprototypeof: private
  regexp.prototype.flags@1.5.4:
    regexp.prototype.flags: private
  relateurl@0.2.7:
    relateurl: private
  renderkid@2.0.7:
    renderkid: private
  request-progress@3.0.0:
    request-progress: private
  require-directory@2.1.1:
    require-directory: private
  require-from-string@2.0.2:
    require-from-string: private
  requires-port@1.0.0:
    requires-port: private
  reselect@5.1.1:
    reselect: private
  resize-observer-polyfill@1.5.1:
    resize-observer-polyfill: private
  resolve-cwd@3.0.0:
    resolve-cwd: private
  resolve-from@4.0.0:
    resolve-from: private
  restore-cursor@3.1.0:
    restore-cursor: private
  retry@0.13.1:
    retry: private
  reusify@1.1.0:
    reusify: private
  rfdc@1.4.1:
    rfdc: private
  rollup@4.45.1:
    rollup: private
  rrweb-cssom@0.8.0:
    rrweb-cssom: private
  run-applescript@7.0.0:
    run-applescript: private
  run-parallel@1.2.0:
    run-parallel: private
  rw@1.3.3:
    rw: private
  rxjs@7.8.2:
    rxjs: private
  safe-array-concat@1.1.3:
    safe-array-concat: private
  safe-buffer@5.2.1:
    safe-buffer: private
  safe-push-apply@1.0.0:
    safe-push-apply: private
  safe-regex-test@1.1.0:
    safe-regex-test: private
  safer-buffer@2.1.2:
    safer-buffer: private
  saxes@6.0.0:
    saxes: private
  scheduler@0.26.0:
    scheduler: private
  schema-utils@4.3.2:
    schema-utils: private
  scroll-into-view-if-needed@3.1.0:
    scroll-into-view-if-needed: private
  select-hose@2.0.0:
    select-hose: private
  selfsigned@2.4.1:
    selfsigned: private
  semver@7.7.2:
    semver: private
  send@0.19.0:
    send: private
  serialize-javascript@6.0.2:
    serialize-javascript: private
  serve-index@1.9.1:
    serve-index: private
  serve-static@1.16.2:
    serve-static: private
  set-cookie-parser@2.7.1:
    set-cookie-parser: private
  set-function-length@1.2.2:
    set-function-length: private
  set-function-name@2.0.2:
    set-function-name: private
  set-proto@1.0.0:
    set-proto: private
  setprototypeof@1.2.0:
    setprototypeof: private
  shallow-clone@3.0.1:
    shallow-clone: private
  shallowequal@1.1.0:
    shallowequal: private
  shebang-command@2.0.0:
    shebang-command: private
  shebang-regex@3.0.0:
    shebang-regex: private
  shell-quote@1.8.3:
    shell-quote: private
  side-channel-list@1.0.0:
    side-channel-list: private
  side-channel-map@1.0.1:
    side-channel-map: private
  side-channel-weakmap@1.0.2:
    side-channel-weakmap: private
  side-channel@1.1.0:
    side-channel: private
  signal-exit@3.0.7:
    signal-exit: private
  simple-swizzle@0.2.2:
    simple-swizzle: private
  sirv@2.0.4:
    sirv: private
  size-sensor@1.0.2:
    size-sensor: private
  slash@3.0.0:
    slash: private
  slice-ansi@3.0.0:
    slice-ansi: private
  sockjs@0.3.24:
    sockjs: private
  source-map-js@1.2.1:
    source-map-js: private
  source-map-support@0.5.21:
    source-map-support: private
  source-map@0.6.1:
    source-map: private
  spdy-transport@3.0.0:
    spdy-transport: private
  spdy@4.0.2:
    spdy: private
  speed-measure-webpack-plugin@1.4.2(webpack@5.100.2):
    speed-measure-webpack-plugin: private
  sprintf-js@1.0.3:
    sprintf-js: private
  sshpk@1.18.0:
    sshpk: private
  stack-utils@2.0.6:
    stack-utils: private
  statuses@2.0.2:
    statuses: private
  stop-iteration-iterator@1.1.0:
    stop-iteration-iterator: private
  strict-event-emitter@0.5.1:
    strict-event-emitter: private
  string-convert@0.2.1:
    string-convert: private
  string-length@4.0.2:
    string-length: private
  string-width@4.2.3:
    string-width: private
    string-width-cjs: private
  string.prototype.trim@1.2.10:
    string.prototype.trim: private
  string.prototype.trimend@1.0.9:
    string.prototype.trimend: private
  string.prototype.trimstart@1.0.8:
    string.prototype.trimstart: private
  string_decoder@1.3.0:
    string_decoder: private
  strip-ansi@6.0.1:
    strip-ansi: private
    strip-ansi-cjs: private
  strip-bom@4.0.0:
    strip-bom: private
  strip-final-newline@2.0.0:
    strip-final-newline: private
  strip-indent@3.0.0:
    strip-indent: private
  strip-json-comments@3.1.1:
    strip-json-comments: private
  styled-components@6.1.19(react-dom@19.1.0(react@19.1.0))(react@19.1.0):
    styled-components: private
  stylis@4.3.6:
    stylis: private
  superagent@10.2.2:
    superagent: private
  supports-color@8.1.1:
    supports-color: private
  svg-path-parser@1.1.0:
    svg-path-parser: private
  symbol-tree@3.2.4:
    symbol-tree: private
  synckit@0.11.8:
    synckit: private
  tapable@1.1.3:
    tapable: private
  terser-webpack-plugin@5.3.14(webpack@5.100.2):
    terser-webpack-plugin: private
  terser@5.43.1:
    terser: private
  test-exclude@6.0.0:
    test-exclude: private
  thingies@1.21.0(tslib@2.8.1):
    thingies: private
  throttle-debounce@5.0.2:
    throttle-debounce: private
  throttleit@1.0.1:
    throttleit: private
  through@2.3.8:
    through: private
  thunky@1.1.0:
    thunky: private
  tinyglobby@0.2.14:
    tinyglobby: private
  tldts-core@6.1.86:
    tldts-core: private
  tldts@6.1.86:
    tldts: private
  tmp@0.2.3:
    tmp: private
  tmpl@1.0.5:
    tmpl: private
  to-regex-range@5.0.1:
    to-regex-range: private
  toggle-selection@1.0.6:
    toggle-selection: private
  toidentifier@1.0.1:
    toidentifier: private
  totalist@3.0.1:
    totalist: private
  tough-cookie@4.1.4:
    tough-cookie: private
  tr46@5.1.1:
    tr46: private
  tree-dump@1.0.3(tslib@2.8.1):
    tree-dump: private
  tree-kill@1.2.2:
    tree-kill: private
  ts-api-utils@2.1.0(typescript@5.8.3):
    ts-api-utils: private
  tslib@2.8.1:
    tslib: private
  tunnel-agent@0.6.0:
    tunnel-agent: private
  tweetnacl@0.14.5:
    tweetnacl: private
  type-check@0.4.0:
    type-check: private
  type-detect@4.0.8:
    type-detect: private
  type-fest@4.41.0:
    type-fest: private
  type-is@1.6.18:
    type-is: private
  typed-array-buffer@1.0.3:
    typed-array-buffer: private
  typed-array-byte-length@1.0.3:
    typed-array-byte-length: private
  typed-array-byte-offset@1.0.4:
    typed-array-byte-offset: private
  typed-array-length@1.0.7:
    typed-array-length: private
  unbox-primitive@1.1.0:
    unbox-primitive: private
  undici-types@7.8.0:
    undici-types: private
  universalify@2.0.1:
    universalify: private
  unpipe@1.0.0:
    unpipe: private
  unrs-resolver@1.11.1:
    unrs-resolver: private
  untildify@4.0.0:
    untildify: private
  update-browserslist-db@1.1.3(browserslist@4.25.1):
    update-browserslist-db: private
  uri-js@4.4.1:
    uri-js: private
  url-parse@1.5.10:
    url-parse: private
  use-sync-external-store@1.5.0(react@19.1.0):
    use-sync-external-store: private
  util-deprecate@1.0.2:
    util-deprecate: private
  util.promisify@1.0.0:
    util.promisify: private
  utila@0.4.0:
    utila: private
  utils-merge@1.0.1:
    utils-merge: private
  uuid@8.3.2:
    uuid: private
  v8-to-istanbul@9.3.0:
    v8-to-istanbul: private
  vary@1.1.2:
    vary: private
  verror@1.10.0:
    verror: private
  w3c-xmlserializer@5.0.0:
    w3c-xmlserializer: private
  walker@1.0.8:
    walker: private
  watchpack@2.4.4:
    watchpack: private
  wbuf@1.7.3:
    wbuf: private
  webidl-conversions@7.0.0:
    webidl-conversions: private
  webpack-bundle-analyzer@4.10.2:
    webpack-bundle-analyzer: private
  webpack-dev-middleware@7.4.2(webpack@5.100.2):
    webpack-dev-middleware: private
  webpack-dev-server@5.2.2(webpack@5.100.2):
    webpack-dev-server: private
  webpack-merge@5.10.0:
    webpack-merge: private
  webpack-sources@3.3.3:
    webpack-sources: private
  webpack@5.100.2:
    webpack: private
  websocket-driver@0.7.4:
    websocket-driver: private
  websocket-extensions@0.1.4:
    websocket-extensions: private
  whatwg-encoding@3.1.1:
    whatwg-encoding: private
  whatwg-mimetype@4.0.0:
    whatwg-mimetype: private
  whatwg-url@14.2.0:
    whatwg-url: private
  which-boxed-primitive@1.1.1:
    which-boxed-primitive: private
  which-builtin-type@1.2.1:
    which-builtin-type: private
  which-collection@1.0.2:
    which-collection: private
  which-typed-array@1.1.19:
    which-typed-array: private
  which@2.0.2:
    which: private
  wildcard@2.0.1:
    wildcard: private
  word-wrap@1.2.5:
    word-wrap: private
  workerize-loader@2.0.2(webpack@5.100.2):
    workerize-loader: private
  wrap-ansi@7.0.0:
    wrap-ansi: private
    wrap-ansi-cjs: private
  wrappy@1.0.2:
    wrappy: private
  write-file-atomic@5.0.1:
    write-file-atomic: private
  ws@8.18.3:
    ws: private
  wsl-utils@0.1.0:
    wsl-utils: private
  xml-name-validator@5.0.0:
    xml-name-validator: private
  xmlchars@2.2.0:
    xmlchars: private
  y18n@5.0.8:
    y18n: private
  yallist@3.1.1:
    yallist: private
  yargs-parser@21.1.1:
    yargs-parser: private
  yargs@17.7.2:
    yargs: private
  yauzl@2.10.0:
    yauzl: private
  yocto-queue@0.1.0:
    yocto-queue: private
  yoctocolors-cjs@2.1.2:
    yoctocolors-cjs: private
  zrender@5.6.1:
    zrender: private
ignoredBuilds:
  - cypress
  - unrs-resolver
  - msw
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@10.11.0
pendingBuilds: []
prunedAt: Fri, 18 Jul 2025 14:18:33 GMT
publicHoistPattern: []
registries:
  '@jsr': https://npm.jsr.io/
  default: https://registry.npmjs.org/
skipped:
  - '@emnapi/core@1.4.5'
  - '@emnapi/runtime@1.4.5'
  - '@emnapi/wasi-threads@1.0.4'
  - '@esbuild/aix-ppc64@0.25.6'
  - '@esbuild/android-arm64@0.25.6'
  - '@esbuild/android-arm@0.25.6'
  - '@esbuild/android-x64@0.25.6'
  - '@esbuild/darwin-arm64@0.25.6'
  - '@esbuild/darwin-x64@0.25.6'
  - '@esbuild/freebsd-arm64@0.25.6'
  - '@esbuild/freebsd-x64@0.25.6'
  - '@esbuild/linux-arm64@0.25.6'
  - '@esbuild/linux-arm@0.25.6'
  - '@esbuild/linux-ia32@0.25.6'
  - '@esbuild/linux-loong64@0.25.6'
  - '@esbuild/linux-mips64el@0.25.6'
  - '@esbuild/linux-ppc64@0.25.6'
  - '@esbuild/linux-riscv64@0.25.6'
  - '@esbuild/linux-s390x@0.25.6'
  - '@esbuild/linux-x64@0.25.6'
  - '@esbuild/netbsd-arm64@0.25.6'
  - '@esbuild/netbsd-x64@0.25.6'
  - '@esbuild/openbsd-arm64@0.25.6'
  - '@esbuild/openbsd-x64@0.25.6'
  - '@esbuild/openharmony-arm64@0.25.6'
  - '@esbuild/sunos-x64@0.25.6'
  - '@esbuild/win32-arm64@0.25.6'
  - '@esbuild/win32-ia32@0.25.6'
  - '@napi-rs/wasm-runtime@0.2.12'
  - '@rollup/rollup-android-arm-eabi@4.45.1'
  - '@rollup/rollup-android-arm64@4.45.1'
  - '@rollup/rollup-darwin-arm64@4.45.1'
  - '@rollup/rollup-darwin-x64@4.45.1'
  - '@rollup/rollup-freebsd-arm64@4.45.1'
  - '@rollup/rollup-freebsd-x64@4.45.1'
  - '@rollup/rollup-linux-arm-gnueabihf@4.45.1'
  - '@rollup/rollup-linux-arm-musleabihf@4.45.1'
  - '@rollup/rollup-linux-arm64-gnu@4.45.1'
  - '@rollup/rollup-linux-arm64-musl@4.45.1'
  - '@rollup/rollup-linux-loongarch64-gnu@4.45.1'
  - '@rollup/rollup-linux-powerpc64le-gnu@4.45.1'
  - '@rollup/rollup-linux-riscv64-gnu@4.45.1'
  - '@rollup/rollup-linux-riscv64-musl@4.45.1'
  - '@rollup/rollup-linux-s390x-gnu@4.45.1'
  - '@rollup/rollup-linux-x64-gnu@4.45.1'
  - '@rollup/rollup-linux-x64-musl@4.45.1'
  - '@rollup/rollup-win32-arm64-msvc@4.45.1'
  - '@rollup/rollup-win32-ia32-msvc@4.45.1'
  - '@tybys/wasm-util@0.10.0'
  - '@unrs/resolver-binding-android-arm-eabi@1.11.1'
  - '@unrs/resolver-binding-android-arm64@1.11.1'
  - '@unrs/resolver-binding-darwin-arm64@1.11.1'
  - '@unrs/resolver-binding-darwin-x64@1.11.1'
  - '@unrs/resolver-binding-freebsd-x64@1.11.1'
  - '@unrs/resolver-binding-linux-arm-gnueabihf@1.11.1'
  - '@unrs/resolver-binding-linux-arm-musleabihf@1.11.1'
  - '@unrs/resolver-binding-linux-arm64-gnu@1.11.1'
  - '@unrs/resolver-binding-linux-arm64-musl@1.11.1'
  - '@unrs/resolver-binding-linux-ppc64-gnu@1.11.1'
  - '@unrs/resolver-binding-linux-riscv64-gnu@1.11.1'
  - '@unrs/resolver-binding-linux-riscv64-musl@1.11.1'
  - '@unrs/resolver-binding-linux-s390x-gnu@1.11.1'
  - '@unrs/resolver-binding-linux-x64-gnu@1.11.1'
  - '@unrs/resolver-binding-linux-x64-musl@1.11.1'
  - '@unrs/resolver-binding-wasm32-wasi@1.11.1'
  - '@unrs/resolver-binding-win32-arm64-msvc@1.11.1'
  - '@unrs/resolver-binding-win32-ia32-msvc@1.11.1'
  - fsevents@2.3.3
storeDir: C:\Users\<USER>\AppData\Local\pnpm\store\v10
virtualStoreDir: C:\workspaces\dd\mbdp\frontend\node_modules\.pnpm
virtualStoreDirMaxLength: 60
