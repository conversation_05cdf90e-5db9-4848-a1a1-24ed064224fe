"use strict";

const http2 = require('http2');
const Stream = require('stream');
const net = require('net');
const tls = require('tls');
const {
  HTTP2_HEADER_PATH,
  HTTP2_HEADER_STATUS,
  HTTP2_HEADER_METHOD,
  HTTP2_HEADER_AUTHORITY,
  HTTP2_HEADER_HOST,
  HTTP2_HEADER_SET_COOKIE,
  NGHTTP2_CANCEL
} = http2.constants;
function setProtocol(protocol) {
  return {
    request(options) {
      return new Request(protocol, options);
    }
  };
}
function normalizeIpv6Host(host) {
  return net.isIP(host) === 6 ? `[${host}]` : host;
}
class Request extends Stream {
  constructor(protocol, options) {
    super();
    const defaultPort = protocol === 'https:' ? 443 : 80;
    const defaultHost = 'localhost';
    const port = options.port || defaultPort;
    const host = options.host || defaultHost;
    delete options.port;
    delete options.host;
    this.method = options.method;
    this.path = options.path;
    this.protocol = protocol;
    this.host = host;
    delete options.method;
    delete options.path;
    const sessionOptions = {
      ...options
    };
    if (options.socketPath) {
      sessionOptions.socketPath = options.socketPath;
      sessionOptions.createConnection = this.createUnixConnection.bind(this);
    }
    this._headers = {};
    const normalizedHost = normalizeIpv6Host(host);
    const session = http2.connect(`${protocol}//${normalizedHost}:${port}`, sessionOptions);
    this.setHeader('host', `${normalizedHost}:${port}`);
    session.on('error', error => this.emit('error', error));
    this.session = session;
  }
  createUnixConnection(authority, options) {
    switch (this.protocol) {
      case 'http:':
        return net.connect(options.socketPath);
      case 'https:':
        options.ALPNProtocols = ['h2'];
        options.servername = this.host;
        options.allowHalfOpen = true;
        return tls.connect(options.socketPath, options);
      default:
        throw new Error('Unsupported protocol', this.protocol);
    }
  }
  setNoDelay(bool) {
    // We can not use setNoDelay with HTTP/2.
    // Node 10 limits http2session.socket methods to ones safe to use with HTTP/2.
    // See also https://nodejs.org/api/http2.html#http2_http2session_socket
  }
  getFrame() {
    if (this.frame) {
      return this.frame;
    }
    const method = {
      [HTTP2_HEADER_PATH]: this.path,
      [HTTP2_HEADER_METHOD]: this.method
    };
    let headers = this.mapToHttp2Header(this._headers);
    headers = Object.assign(headers, method);
    const frame = this.session.request(headers);
    frame.once('response', (headers, flags) => {
      headers = this.mapToHttpHeader(headers);
      frame.headers = headers;
      frame.statusCode = headers[HTTP2_HEADER_STATUS];
      frame.status = frame.statusCode;
      this.emit('response', frame);
    });
    this._headerSent = true;
    frame.once('drain', () => this.emit('drain'));
    frame.on('error', error => this.emit('error', error));
    frame.on('close', () => this.session.close());
    this.frame = frame;
    return frame;
  }
  mapToHttpHeader(headers) {
    const keys = Object.keys(headers);
    const http2Headers = {};
    for (let key of keys) {
      let value = headers[key];
      key = key.toLowerCase();
      switch (key) {
        case HTTP2_HEADER_SET_COOKIE:
          value = Array.isArray(value) ? value : [value];
          break;
        default:
          break;
      }
      http2Headers[key] = value;
    }
    return http2Headers;
  }
  mapToHttp2Header(headers) {
    const keys = Object.keys(headers);
    const http2Headers = {};
    for (let key of keys) {
      let value = headers[key];
      key = key.toLowerCase();
      switch (key) {
        case HTTP2_HEADER_HOST:
          key = HTTP2_HEADER_AUTHORITY;
          value = /^http:\/\/|^https:\/\//.test(value) ? new URL(value).host : value;
          break;
        default:
          break;
      }
      http2Headers[key] = value;
    }
    return http2Headers;
  }
  setHeader(name, value) {
    this._headers[name.toLowerCase()] = value;
  }
  getHeader(name) {
    return this._headers[name.toLowerCase()];
  }
  write(data, encoding) {
    const frame = this.getFrame();
    return frame.write(data, encoding);
  }
  pipe(stream, options) {
    const frame = this.getFrame();
    return frame.pipe(stream, options);
  }
  end(data) {
    const frame = this.getFrame();
    frame.end(data);
  }
  abort(data) {
    const frame = this.getFrame();
    frame.close(NGHTTP2_CANCEL);
    this.session.destroy();
  }
}
exports.setProtocol = setProtocol;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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