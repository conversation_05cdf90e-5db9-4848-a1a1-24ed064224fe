@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=C:\workspaces\dd\mbdp\frontend\node_modules\.pnpm\d3-dsv@3.0.1\node_modules\d3-dsv\bin\node_modules;C:\workspaces\dd\mbdp\frontend\node_modules\.pnpm\d3-dsv@3.0.1\node_modules\d3-dsv\node_modules;C:\workspaces\dd\mbdp\frontend\node_modules\.pnpm\d3-dsv@3.0.1\node_modules;C:\workspaces\dd\mbdp\frontend\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=C:\workspaces\dd\mbdp\frontend\node_modules\.pnpm\d3-dsv@3.0.1\node_modules\d3-dsv\bin\node_modules;C:\workspaces\dd\mbdp\frontend\node_modules\.pnpm\d3-dsv@3.0.1\node_modules\d3-dsv\node_modules;C:\workspaces\dd\mbdp\frontend\node_modules\.pnpm\d3-dsv@3.0.1\node_modules;C:\workspaces\dd\mbdp\frontend\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\d3-dsv\bin\dsv2dsv.js" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\d3-dsv\bin\dsv2dsv.js" %*
)
