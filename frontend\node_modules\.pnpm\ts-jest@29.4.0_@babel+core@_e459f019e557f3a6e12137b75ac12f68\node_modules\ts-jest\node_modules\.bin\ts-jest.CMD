@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=C:\workspaces\dd\mbdp\frontend\node_modules\.pnpm\ts-jest@29.4.0_@babel+core@_e459f019e557f3a6e12137b75ac12f68\node_modules\ts-jest\node_modules;C:\workspaces\dd\mbdp\frontend\node_modules\.pnpm\ts-jest@29.4.0_@babel+core@_e459f019e557f3a6e12137b75ac12f68\node_modules;C:\workspaces\dd\mbdp\frontend\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=C:\workspaces\dd\mbdp\frontend\node_modules\.pnpm\ts-jest@29.4.0_@babel+core@_e459f019e557f3a6e12137b75ac12f68\node_modules\ts-jest\node_modules;C:\workspaces\dd\mbdp\frontend\node_modules\.pnpm\ts-jest@29.4.0_@babel+core@_e459f019e557f3a6e12137b75ac12f68\node_modules;C:\workspaces\dd\mbdp\frontend\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\..\cli.js" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\..\cli.js" %*
)
